package com.amobear.themepack.data.network

import com.amobear.themepack.data.model.*
import retrofit2.Response
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Mock implementation of ApiServiceNoIds for testing and development
 * Returns hardcoded data that simulates API responses without IDs
 */
@Singleton
class MockApiServiceNoIds @Inject constructor() : ApiServiceNoIds {

    override suspend fun getThemes(): Response<ApiResponse<ThemesApiResponseNoId>> {
        val mockResponse = ApiResponse(
            success = true,
            data = ThemesApiResponseNoId(
                categories = listOf(
                    ApiThemeCategoryNoId(
                        title = "Aesthetic",
                        titleIconUrl = "https://example.com/aesthetic_icon.png",
                        weight = 10,
                        themes = listOf(
                            ApiThemeNoId(
                                title = "Pink Aesthetic",
                                description = "Beautiful pink aesthetic theme with soft colors",
                                previewImage = "https://example.com/pink_aesthetic.jpg",
                                wallpaperPacks = listOf(
                                    ApiWallpaperPackNoId(
                                        title = "Pink Wallpapers",
                                        previewImage = "https://example.com/pink_pack.jpg",
                                        weight = 5,
                                        coin = 100,
                                        wallpapers = listOf(
                                            ApiWallpaperNoId(
                                                title = "Pink Sunset",
                                                imageUrl = "https://example.com/pink_sunset.jpg",
                                                description = "Beautiful pink sunset wallpaper"
                                            ),
                                            ApiWallpaperNoId(
                                                title = "Pink Flowers",
                                                imageUrl = "https://example.com/pink_flowers.jpg",
                                                description = "Soft pink flower wallpaper"
                                            )
                                        )
                                    )
                                ),
                                widgetPacks = listOf(
                                    ApiWidgetPackNoId(
                                        title = "Pink Widgets",
                                        previewImage = "https://example.com/pink_widgets.jpg",
                                        weight = 3,
                                        coin = 50,
                                        type = "clock",
                                        widgets = listOf(
                                            ApiWidgetNoId(
                                                title = "Pink Clock",
                                                previewImage = "https://example.com/pink_clock.jpg",
                                                width = 4,
                                                height = 2,
                                                size = "medium"
                                            )
                                        ),
                                        compatibleThemes = listOf(
                                            ApiCompatibleThemeNoId(title = "Pink Aesthetic")
                                        )
                                    )
                                ),
                                iconPacks = listOf(
                                    ApiIconPackNoId(
                                        title = "Pink Icons",
                                        previewImage = "https://example.com/pink_icons.jpg",
                                        weight = 2,
                                        coin = 75,
                                        icons = listOf(
                                            ApiIconNoId(
                                                appId = "com.android.chrome",
                                                name = "Chrome",
                                                imageUrl = "https://example.com/pink_chrome.png"
                                            ),
                                            ApiIconNoId(
                                                appId = "com.instagram.android",
                                                name = "Instagram",
                                                imageUrl = "https://example.com/pink_instagram.png"
                                            )
                                        ),
                                        compatibleThemes = listOf(
                                            ApiCompatibleThemeNoId(title = "Pink Aesthetic")
                                        )
                                    )
                                )
                            ),
                            ApiThemeNoId(
                                title = "Blue Aesthetic",
                                description = "Cool blue aesthetic theme with ocean vibes",
                                previewImage = "https://example.com/blue_aesthetic.jpg",
                                wallpaperPacks = listOf(
                                    ApiWallpaperPackNoId(
                                        title = "Ocean Wallpapers",
                                        previewImage = "https://example.com/ocean_pack.jpg",
                                        weight = 4,
                                        coin = 120,
                                        wallpapers = listOf(
                                            ApiWallpaperNoId(
                                                title = "Ocean Waves",
                                                imageUrl = "https://example.com/ocean_waves.jpg",
                                                description = "Calming ocean waves wallpaper"
                                            )
                                        )
                                    )
                                ),
                                widgetPacks = emptyList(),
                                iconPacks = emptyList()
                            )
                        )
                    ),
                    ApiThemeCategoryNoId(
                        title = "K-Pop",
                        titleIconUrl = "https://example.com/kpop_icon.png",
                        weight = 8,
                        themes = listOf(
                            ApiThemeNoId(
                                title = "BTS Theme",
                                description = "Official BTS themed pack",
                                previewImage = "https://example.com/bts_theme.jpg",
                                wallpaperPacks = listOf(
                                    ApiWallpaperPackNoId(
                                        title = "BTS Wallpapers",
                                        previewImage = "https://example.com/bts_pack.jpg",
                                        weight = 10,
                                        coin = 200,
                                        wallpapers = listOf(
                                            ApiWallpaperNoId(
                                                title = "BTS Group Photo",
                                                imageUrl = "https://example.com/bts_group.jpg",
                                                description = "Official BTS group photo"
                                            ),
                                            ApiWallpaperNoId(
                                                title = "BTS Logo",
                                                imageUrl = "https://example.com/bts_logo.jpg",
                                                description = "BTS official logo wallpaper"
                                            )
                                        )
                                    )
                                ),
                                widgetPacks = emptyList(),
                                iconPacks = emptyList()
                            )
                        )
                    )
                )
            ),
            error = null
        )

        return Response.success(mockResponse)
    }

    override suspend fun getWidgetPacks(): Response<ApiResponse<WidgetPacksApiResponseNoId>> {
        val mockResponse = ApiResponse(
            success = true,
            data = WidgetPacksApiResponseNoId(
                categories = listOf(
                    ApiWidgetCategoryNoId(
                        title = "Clock Widgets",
                        titleIconUrl = "https://example.com/clock_icon.png",
                        weight = 10,
                        widgetPacks = listOf(
                            ApiWidgetPackNoId(
                                title = "Digital Clocks",
                                previewImage = "https://example.com/digital_clocks.jpg",
                                weight = 5,
                                coin = 50,
                                type = "clock",
                                widgets = listOf(
                                    ApiWidgetNoId(
                                        title = "Simple Digital Clock",
                                        previewImage = "https://example.com/simple_clock.jpg",
                                        width = 4,
                                        height = 2,
                                        size = "medium"
                                    ),
                                    ApiWidgetNoId(
                                        title = "Fancy Digital Clock",
                                        previewImage = "https://example.com/fancy_clock.jpg",
                                        width = 4,
                                        height = 3,
                                        size = "large"
                                    )
                                ),
                                compatibleThemes = listOf(
                                    ApiCompatibleThemeNoId(title = "Pink Aesthetic"),
                                    ApiCompatibleThemeNoId(title = "Blue Aesthetic")
                                )
                            )
                        )
                    ),
                    ApiWidgetCategoryNoId(
                        title = "Weather Widgets",
                        titleIconUrl = "https://example.com/weather_icon.png",
                        weight = 8,
                        widgetPacks = listOf(
                            ApiWidgetPackNoId(
                                title = "Modern Weather",
                                previewImage = "https://example.com/modern_weather.jpg",
                                weight = 3,
                                coin = 75,
                                type = "weather",
                                widgets = listOf(
                                    ApiWidgetNoId(
                                        title = "Weather Card",
                                        previewImage = "https://example.com/weather_card.jpg",
                                        width = 4,
                                        height = 2,
                                        size = "medium"
                                    )
                                ),
                                compatibleThemes = emptyList()
                            )
                        )
                    )
                )
            ),
            error = null
        )

        return Response.success(mockResponse)
    }

    override suspend fun getIconPacks(): Response<ApiResponse<IconPacksApiResponseNoId>> {
        val mockResponse = ApiResponse(
            success = true,
            data = IconPacksApiResponseNoId(
                categories = listOf(
                    ApiIconCategoryNoId(
                        title = "Aesthetic Icons",
                        titleIconUrl = "https://example.com/aesthetic_icons.png",
                        weight = 10,
                        iconPacks = listOf(
                            ApiIconPackNoId(
                                title = "Pastel Icons",
                                previewImage = "https://example.com/pastel_icons.jpg",
                                weight = 5,
                                coin = 100,
                                icons = listOf(
                                    ApiIconNoId(
                                        appId = "com.android.chrome",
                                        name = "Chrome",
                                        imageUrl = "https://example.com/pastel_chrome.png"
                                    ),
                                    ApiIconNoId(
                                        appId = "com.instagram.android",
                                        name = "Instagram",
                                        imageUrl = "https://example.com/pastel_instagram.png"
                                    ),
                                    ApiIconNoId(
                                        appId = "com.spotify.music",
                                        name = "Spotify",
                                        imageUrl = "https://example.com/pastel_spotify.png"
                                    )
                                ),
                                compatibleThemes = listOf(
                                    ApiCompatibleThemeNoId(title = "Pink Aesthetic"),
                                    ApiCompatibleThemeNoId(title = "Blue Aesthetic")
                                )
                            )
                        )
                    ),
                    ApiIconCategoryNoId(
                        title = "K-Pop Icons",
                        titleIconUrl = "https://example.com/kpop_icons.png",
                        weight = 8,
                        iconPacks = listOf(
                            ApiIconPackNoId(
                                title = "BTS Icons",
                                previewImage = "https://example.com/bts_icons.jpg",
                                weight = 10,
                                coin = 150,
                                icons = listOf(
                                    ApiIconNoId(
                                        appId = "com.android.chrome",
                                        name = "Chrome",
                                        imageUrl = "https://example.com/bts_chrome.png"
                                    ),
                                    ApiIconNoId(
                                        appId = "com.instagram.android",
                                        name = "Instagram",
                                        imageUrl = "https://example.com/bts_instagram.png"
                                    )
                                ),
                                compatibleThemes = listOf(
                                    ApiCompatibleThemeNoId(title = "BTS Theme")
                                )
                            )
                        )
                    )
                )
            ),
            error = null
        )

        return Response.success(mockResponse)
    }
}
