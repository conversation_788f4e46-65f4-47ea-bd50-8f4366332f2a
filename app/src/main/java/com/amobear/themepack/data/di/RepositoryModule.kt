package com.amobear.themepack.data.di

import com.amobear.themepack.data.datalocal.IconCategoryDao
import com.amobear.themepack.data.datalocal.IconDao
import com.amobear.themepack.data.datalocal.ThemeAppDatabase
import com.amobear.themepack.data.datalocal.ThemeCategoryDao
import com.amobear.themepack.data.datalocal.ThemeDao
import com.amobear.themepack.data.datalocal.WallpaperDao
import com.amobear.themepack.data.datalocal.WidgetCategoryDao
import com.amobear.themepack.data.datalocal.WidgetDao
import com.amobear.themepack.data.datalocal.sharepref.SharePreferenceProvider
import com.amobear.themepack.data.repository.DownloadedWallpaperRepository
import com.amobear.themepack.data.repository.DownloadedWallpaperRepositoryImpl
import com.amobear.themepack.data.repository.ThemeRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object RepositoryModule {

    @Provides
    @Singleton
    fun provideNewThemeDao(database: ThemeAppDatabase): ThemeDao {
        return database.themeDao()
    }

    @Provides
    @Singleton
    fun provideThemeCategoryDao(database: ThemeAppDatabase): ThemeCategoryDao {
        return database.themeCategoryDao()
    }

    @Provides
    @Singleton
    fun provideWallpaperDao(database: ThemeAppDatabase): WallpaperDao {
        return database.wallpaperDao()
    }

    @Provides
    @Singleton
    fun provideWidgetCategoryDao(database: ThemeAppDatabase): WidgetCategoryDao {
        return database.widgetCategoryDao()
    }

    @Provides
    @Singleton
    fun provideWidgetPackDao(database: ThemeAppDatabase): com.amobear.themepack.data.datalocal.WidgetPackDao {
        return database.widgetPackDao()
    }

    @Provides
    @Singleton
    fun provideWidgetDao(database: ThemeAppDatabase): WidgetDao {
        return database.widgetDao()
    }

    @Provides
    @Singleton
    fun provideIconCategoryDao(database: ThemeAppDatabase): IconCategoryDao {
        return database.iconCategoryDao()
    }

    @Provides
    @Singleton
    fun provideIconPackDao(database: ThemeAppDatabase): com.amobear.themepack.data.datalocal.IconPackDao {
        return database.iconPackDao()
    }

    @Provides
    @Singleton
    fun provideIconDao(database: ThemeAppDatabase): IconDao {
        return database.iconDao()
    }

    @Provides
    @Singleton
    fun provideNewThemeRepository(
        apiService: com.amobear.themepack.data.network.ApiService,
        database: ThemeAppDatabase
    ): ThemeRepository {
        return ThemeRepository(
            apiService,
            database
        )
    }

    // TODO: Temporarily commented out to fix build
    // @Provides
    // @Singleton
    // fun provideStatelessThemeRepository(
    //     apiService: com.amobear.themepack.data.network.ApiService,
    //     database: ThemeAppDatabase
    // ): com.amobear.themepack.data.repository.StatelessThemeRepository {
    //     return com.amobear.themepack.data.repository.StatelessThemeRepository(
    //         apiService,
    //         database
    //     )
    // }

    // @Provides
    // @Singleton
    // fun provideUnlockStateRepository(
    //     database: ThemeAppDatabase
    // ): com.amobear.themepack.data.repository.UnlockStateRepository {
    //     return com.amobear.themepack.data.repository.UnlockStateRepository(
    //         database
    //     )
    // }

    // @Provides
    // @Singleton
    // fun provideCompositeThemeRepository(
    //     statelessThemeRepository: com.amobear.themepack.data.repository.StatelessThemeRepository,
    //     unlockStateRepository: com.amobear.themepack.data.repository.UnlockStateRepository
    // ): com.amobear.themepack.data.repository.CompositeThemeRepository {
    //     return com.amobear.themepack.data.repository.CompositeThemeRepository(
    //         statelessThemeRepository,
    //         unlockStateRepository
    //     )
    // }
    
    @Provides
    @Singleton
    fun provideDownloadedWallpaperRepository(
        preferenceProvider: SharePreferenceProvider
    ): DownloadedWallpaperRepository {
        return DownloadedWallpaperRepositoryImpl(preferenceProvider)
    }
}
