package com.amobear.themepack.data.repository

import com.amobear.themepack.data.datalocal.ThemeAppDatabase
import com.amobear.themepack.data.mapper.toDomain
import com.amobear.themepack.data.mapper.toEntity
import com.amobear.themepack.data.model.IconCategory
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.ThemeCategory
import com.amobear.themepack.data.model.WidgetCategory
import com.amobear.themepack.data.model.toIconPack
import com.amobear.themepack.data.model.toThemeCategory
import com.amobear.themepack.data.model.toWidgetCategory
import com.amobear.themepack.data.model.toWidgetPack
import com.amobear.themepack.data.network.ApiResult
import com.amobear.themepack.data.network.ApiService
import com.amobear.themepack.data.network.toApiResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for the new consolidated API structure with unlock detection
 */
@Singleton
class ThemeRepository @Inject constructor(
    private val apiService: ApiService,
    private val themeAppDatabase: ThemeAppDatabase,
) {

    val themeCategories =
        themeAppDatabase.themeCategoryDao().getAllCategories().map { it.map { it.toDomain() } }
    val widgetCategories = themeAppDatabase.widgetCategoryDao().getAllWidgetCategories()
        .map { it.map { it.toDomain() } }
    val iconCategories =
        themeAppDatabase.iconCategoryDao().getAllIconCategories().map { it.map { it.toDomain() } }

    /**
     * Get all theme categories and themes with unlock status
     */
    suspend fun getThemeCategories(forceRefresh: Boolean = false): ApiResult<List<ThemeCategory>> {
        return try {
            val response = apiService.getThemes().toApiResult()

            when (response) {
                is ApiResult.Success -> {
                    val themeCategories = response.data.data.categories.mapIndexed { categoryIndex, apiCategory ->
                        apiCategory.toThemeCategory(categoryIndex)
                    }
                    themeAppDatabase.themeCategoryDao().deleteAllCategories()
                    themeAppDatabase.themeCategoryDao()
                        .insertCategories(themeCategories.map { it.toEntity() })
                    ApiResult.Success(themeCategories)
                }

                is ApiResult.Error -> response
                is ApiResult.Loading -> response
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    /**
     * Get all widget categories and widget packs with unlock status
     */
    suspend fun getWidgetCategories(forceRefresh: Boolean = false): ApiResult<List<WidgetCategory>> {

        return try {
            val response = apiService.getWidgetPacks().toApiResult()
            when (response) {
                is ApiResult.Success -> {
                    val widgetCategories = response.data.data.categories.mapIndexed { categoryIndex, apiCategory ->
                        apiCategory.toWidgetCategory(categoryIndex)
                    }
                    themeAppDatabase.widgetCategoryDao().deleteAllWidgetCategories()
                    themeAppDatabase.widgetCategoryDao()
                        .insertWidgetCategories(widgetCategories.map { it.toEntity() })
                    ApiResult.Success(widgetCategories)
                }

                is ApiResult.Error -> response
                is ApiResult.Loading -> response
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    /**
     * Get all icon categories and icon packs with unlock status
     */
    suspend fun getIconCategories(forceRefresh: Boolean = false): ApiResult<List<IconCategory>> {
        return try {
            val response = apiService.getIconPacks().toApiResult()

            when (response) {
                is ApiResult.Success -> {
                    val iconCategories = response.data.data.categories.mapIndexed { categoryIndex, apiCategory ->
                        apiCategory.toIconCategory(categoryIndex)
                    }
                    themeAppDatabase.iconCategoryDao().deleteAllIconCategories()
                    themeAppDatabase.iconCategoryDao()
                        .insertIconCategories(iconCategories.map { it.toEntity() })
                    ApiResult.Success(iconCategories)
                }

                is ApiResult.Error -> response
                is ApiResult.Loading -> response
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    /**
     * Get theme by ID
     */
    fun getThemeById(themeId: Int): Flow<Theme?> = flow {
        val theme = themeAppDatabase.themeDao().getThemeById(themeId)?.toDomain()
        emit(theme)
    }

    /**
     * Get themes by category ID
     */
    fun getThemesByCategoryId(categoryId: Int): Flow<List<Theme>> =
        themeAppDatabase.themeDao().getThemesByCategory(categoryId).map {
            it.map { it.toDomain() }
        }
}