package com.amobear.themepack.data.repository

import com.amobear.themepack.data.model.IconCategory
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.ThemeCategory
import com.amobear.themepack.data.model.WidgetCategory
import com.amobear.themepack.data.network.ApiResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Composite repository that combines stateless data with unlock states
 * This is the main repository that ViewModels should use
 */
@Singleton
class CompositeThemeRepository @Inject constructor(
    private val statelessThemeRepository: StatelessThemeRepository,
    private val unlockStateRepository: UnlockStateRepository
) {

    /**
     * Get theme categories with unlock status applied
     * Combines stateless data with unlock states
     */
    val themeCategoriesWithUnlockStatus: Flow<List<ThemeCategory>> = 
        combine(
            statelessThemeRepository.themeCategories,
            unlockStateRepository.unlockedThemes,
            unlockStateRepository.unlockedWallpaperPacks,
            unlockStateRepository.unlockedWidgetPacks,
            unlockStateRepository.unlockedIconPacks,
            unlockStateRepository.unlockedWallpapers
        ) { categories, unlockedThemes, unlockedWallpaperPacks, unlockedWidgetPacks, unlockedIconPacks, unlockedWallpapers ->
            categories.map { category ->
                category.copy(
                    themes = category.themes.map { theme ->
                        applyUnlockStatusToTheme(
                            theme = theme,
                            unlockedThemes = unlockedThemes,
                            unlockedWallpaperPacks = unlockedWallpaperPacks,
                            unlockedWidgetPacks = unlockedWidgetPacks,
                            unlockedIconPacks = unlockedIconPacks,
                            unlockedWallpapers = unlockedWallpapers
                        )
                    }
                )
            }
        }

    /**
     * Get widget categories with unlock status applied
     */
    val widgetCategoriesWithUnlockStatus: Flow<List<WidgetCategory>> =
        combine(
            statelessThemeRepository.widgetCategories,
            unlockStateRepository.unlockedWidgetPacks
        ) { categories, unlockedWidgetPacks ->
            categories.map { category ->
                category.copy(
                    widgetPacks = category.widgetPacks.map { pack ->
                        pack.copy(isPurchased = unlockedWidgetPacks.contains(pack.id))
                    }
                )
            }
        }

    /**
     * Get icon categories with unlock status applied
     */
    val iconCategoriesWithUnlockStatus: Flow<List<IconCategory>> =
        combine(
            statelessThemeRepository.iconCategories,
            unlockStateRepository.unlockedIconPacks
        ) { categories, unlockedIconPacks ->
            categories.map { category ->
                category.copy(
                    iconPacks = category.iconPacks.map { pack ->
                        pack.copy(isPurchased = unlockedIconPacks.contains(pack.id))
                    }
                )
            }
        }

    /**
     * Fetch theme categories from API and return with unlock status
     */
    suspend fun fetchThemeCategoriesWithUnlockStatus(forceRefresh: Boolean = false): ApiResult<List<ThemeCategory>> {
        return when (val result = statelessThemeRepository.fetchThemeCategories(forceRefresh)) {
            is ApiResult.Success -> {
                // Apply unlock status to the fetched data
                val unlockedThemes = unlockStateRepository.unlockedThemes.value
                val unlockedWallpaperPacks = unlockStateRepository.unlockedWallpaperPacks.value
                val unlockedWidgetPacks = unlockStateRepository.unlockedWidgetPacks.value
                val unlockedIconPacks = unlockStateRepository.unlockedIconPacks.value
                val unlockedWallpapers = unlockStateRepository.unlockedWallpapers.value

                val categoriesWithUnlockStatus = result.data.map { category ->
                    category.copy(
                        themes = category.themes.map { theme ->
                            applyUnlockStatusToTheme(
                                theme = theme,
                                unlockedThemes = unlockedThemes,
                                unlockedWallpaperPacks = unlockedWallpaperPacks,
                                unlockedWidgetPacks = unlockedWidgetPacks,
                                unlockedIconPacks = unlockedIconPacks,
                                unlockedWallpapers = unlockedWallpapers
                            )
                        }
                    )
                }

                ApiResult.Success(categoriesWithUnlockStatus)
            }
            is ApiResult.Error -> result
            is ApiResult.Loading -> result
        }
    }

    /**
     * Fetch widget categories from API and return with unlock status
     */
    suspend fun fetchWidgetCategoriesWithUnlockStatus(forceRefresh: Boolean = false): ApiResult<List<WidgetCategory>> {
        return when (val result = statelessThemeRepository.fetchWidgetCategories(forceRefresh)) {
            is ApiResult.Success -> {
                val unlockedWidgetPacks = unlockStateRepository.unlockedWidgetPacks.value

                val categoriesWithUnlockStatus = result.data.map { category ->
                    category.copy(
                        widgetPacks = category.widgetPacks.map { pack ->
                            pack.copy(isPurchased = unlockedWidgetPacks.contains(pack.id))
                        }
                    )
                }

                ApiResult.Success(categoriesWithUnlockStatus)
            }
            is ApiResult.Error -> result
            is ApiResult.Loading -> result
        }
    }

    /**
     * Fetch icon categories from API and return with unlock status
     */
    suspend fun fetchIconCategoriesWithUnlockStatus(forceRefresh: Boolean = false): ApiResult<List<IconCategory>> {
        return when (val result = statelessThemeRepository.fetchIconCategories(forceRefresh)) {
            is ApiResult.Success -> {
                val unlockedIconPacks = unlockStateRepository.unlockedIconPacks.value

                val categoriesWithUnlockStatus = result.data.map { category ->
                    category.copy(
                        iconPacks = category.iconPacks.map { pack ->
                            pack.copy(isPurchased = unlockedIconPacks.contains(pack.id))
                        }
                    )
                }

                ApiResult.Success(categoriesWithUnlockStatus)
            }
            is ApiResult.Error -> result
            is ApiResult.Loading -> result
        }
    }

    /**
     * Get theme by ID with unlock status applied
     */
    fun getThemeByIdWithUnlockStatus(themeId: Int): Flow<Theme?> =
        combine(
            statelessThemeRepository.getThemeById(themeId),
            unlockStateRepository.unlockedThemes,
            unlockStateRepository.unlockedWallpaperPacks,
            unlockStateRepository.unlockedWidgetPacks,
            unlockStateRepository.unlockedIconPacks,
            unlockStateRepository.unlockedWallpapers
        ) { theme, unlockedThemes, unlockedWallpaperPacks, unlockedWidgetPacks, unlockedIconPacks, unlockedWallpapers ->
            theme?.let {
                applyUnlockStatusToTheme(
                    theme = it,
                    unlockedThemes = unlockedThemes,
                    unlockedWallpaperPacks = unlockedWallpaperPacks,
                    unlockedWidgetPacks = unlockedWidgetPacks,
                    unlockedIconPacks = unlockedIconPacks,
                    unlockedWallpapers = unlockedWallpapers
                )
            }
        }

    /**
     * Get themes by category ID with unlock status applied
     */
    fun getThemesByCategoryIdWithUnlockStatus(categoryId: Int): Flow<List<Theme>> =
        combine(
            statelessThemeRepository.getThemesByCategoryId(categoryId),
            unlockStateRepository.unlockedThemes,
            unlockStateRepository.unlockedWallpaperPacks,
            unlockStateRepository.unlockedWidgetPacks,
            unlockStateRepository.unlockedIconPacks,
            unlockStateRepository.unlockedWallpapers
        ) { themes, unlockedThemes, unlockedWallpaperPacks, unlockedWidgetPacks, unlockedIconPacks, unlockedWallpapers ->
            themes.map { theme ->
                applyUnlockStatusToTheme(
                    theme = theme,
                    unlockedThemes = unlockedThemes,
                    unlockedWallpaperPacks = unlockedWallpaperPacks,
                    unlockedWidgetPacks = unlockedWidgetPacks,
                    unlockedIconPacks = unlockedIconPacks,
                    unlockedWallpapers = unlockedWallpapers
                )
            }
        }

    /**
     * Helper function to apply unlock status to a theme
     */
    private fun applyUnlockStatusToTheme(
        theme: Theme,
        unlockedThemes: Set<Int>,
        unlockedWallpaperPacks: Set<Int>,
        unlockedWidgetPacks: Set<Int>,
        unlockedIconPacks: Set<Int>,
        unlockedWallpapers: Set<String>
    ): Theme {
        return theme.copy(
            isUnlocked = unlockedThemes.contains(theme.id),
            wallpaperPacks = theme.wallpaperPacks.map { pack ->
                pack.copy(
                    isPurchased = unlockedWallpaperPacks.contains(pack.id),
                    wallpapers = pack.wallpapers.map { wallpaper ->
                        wallpaper.copy(isUnlocked = unlockedWallpapers.contains(wallpaper.imageUrl))
                    }
                )
            },
            widgetPacks = theme.widgetPacks.map { pack ->
                pack.copy(isPurchased = unlockedWidgetPacks.contains(pack.id))
            },
            iconPacks = theme.iconPacks.map { pack ->
                pack.copy(isPurchased = unlockedIconPacks.contains(pack.id))
            }
        )
    }

    // ============= UNLOCK OPERATIONS =============

    /**
     * Unlock a theme
     */
    suspend fun unlockTheme(themeId: Int) {
        unlockStateRepository.unlockTheme(themeId)
    }

    /**
     * Unlock a wallpaper pack
     */
    suspend fun unlockWallpaperPack(packId: Int) {
        unlockStateRepository.unlockWallpaperPack(packId)
    }

    /**
     * Unlock a widget pack
     */
    suspend fun unlockWidgetPack(packId: Int) {
        unlockStateRepository.unlockWidgetPack(packId)
    }

    /**
     * Unlock an icon pack
     */
    suspend fun unlockIconPack(packId: Int) {
        unlockStateRepository.unlockIconPack(packId)
    }

    /**
     * Unlock a specific wallpaper
     */
    suspend fun unlockWallpaper(wallpaperUrl: String) {
        unlockStateRepository.unlockWallpaper(wallpaperUrl)
    }

    // ============= CHECK UNLOCK STATUS =============

    /**
     * Check if a theme is unlocked
     */
    fun isThemeUnlocked(themeId: Int): Boolean {
        return unlockStateRepository.isThemeUnlocked(themeId)
    }

    /**
     * Check if a wallpaper pack is unlocked
     */
    fun isWallpaperPackUnlocked(packId: Int): Boolean {
        return unlockStateRepository.isWallpaperPackUnlocked(packId)
    }

    /**
     * Check if a widget pack is unlocked
     */
    fun isWidgetPackUnlocked(packId: Int): Boolean {
        return unlockStateRepository.isWidgetPackUnlocked(packId)
    }

    /**
     * Check if an icon pack is unlocked
     */
    fun isIconPackUnlocked(packId: Int): Boolean {
        return unlockStateRepository.isIconPackUnlocked(packId)
    }

    /**
     * Check if a wallpaper is unlocked
     */
    fun isWallpaperUnlocked(wallpaperUrl: String): Boolean {
        return unlockStateRepository.isWallpaperUnlocked(wallpaperUrl)
    }
}
