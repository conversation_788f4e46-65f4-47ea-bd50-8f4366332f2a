package com.amobear.themepack.data.mapper

import com.amobear.themepack.data.datalocal.IconCategoryEntity
import com.amobear.themepack.data.datalocal.IconEntity
import com.amobear.themepack.data.datalocal.IconPackEntity
import com.amobear.themepack.data.datalocal.ThemeCategoryEntity
import com.amobear.themepack.data.datalocal.ThemeEntity
import com.amobear.themepack.data.datalocal.WallpaperEntity
import com.amobear.themepack.data.datalocal.WallpaperPackEntity
import com.amobear.themepack.data.datalocal.WidgetCategoryEntity
import com.amobear.themepack.data.datalocal.WidgetEntity
import com.amobear.themepack.data.datalocal.WidgetPackEntity
import com.amobear.themepack.data.model.Icon
import com.amobear.themepack.data.model.IconCategory
import com.amobear.themepack.data.model.IconPack
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.ThemeCategory
import com.amobear.themepack.data.model.Wallpaper
import com.amobear.themepack.data.model.WallpaperPack
import com.amobear.themepack.data.model.Widget
import com.amobear.themepack.data.model.WidgetCategory
import com.amobear.themepack.data.model.WidgetPack

// Theme Category mappings
fun ThemeCategoryEntity.toDomain(themes: List<Theme> = emptyList()): ThemeCategory {
    return ThemeCategory(
        id = id,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight,
        themes = themes
    )
}

fun ThemeCategory.toEntity(): ThemeCategoryEntity {
    return ThemeCategoryEntity(
        id = id,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight
    )
}

// Theme mappings
fun ThemeEntity.toDomain(
    wallpaperPacks: List<WallpaperPack> = emptyList(),
    widgetPacks: List<WidgetPack> = emptyList(),
    iconPacks: List<IconPack> = emptyList()
): Theme {
    return Theme(
        id = id,
        title = title,
        description = description,
        previewImage = previewImage,
        categoryId = categoryId,
        wallpaperPacks = wallpaperPacks,
        widgetPacks = widgetPacks,
        iconPacks = iconPacks,
        downloadDate = downloadDate,
        isUnlocked = downloadDate != null, // Derived from downloadDate
        localPath = null,
        isFromCache = false,
        matchConfidence = 1.0f
    )
}

fun Theme.toEntity(): ThemeEntity {
    return ThemeEntity(
        id = id.toInt(),
        title = title,
        description = description,
        previewImage = previewImage,
        categoryId = categoryId,
        downloadDate = downloadDate,
    )
}

// Wallpaper mappings
fun WallpaperPackEntity.toDomain(wallpapers: List<Wallpaper> = emptyList()): WallpaperPack {
    return WallpaperPack(
        id = id,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        wallpapers = wallpapers,
        isPurchased = isPurchased
    )
}

fun WallpaperPack.toEntity(themeId: Int): WallpaperPackEntity {
    return WallpaperPackEntity(
        id = id,
        themeId = themeId,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        isPurchased = isPurchased
    )
}

fun WallpaperEntity.toDomain(): Wallpaper {
    return Wallpaper(
        id = id,
        title = title,
        imageUrl = imageUrl,
        description = description,
        packId = packId,
        localPath = localPath,
        isUnlocked = localPath != null // Derived from localPath
    )
}

fun Wallpaper.toEntity(): WallpaperEntity {
    return WallpaperEntity(
        id = id,
        packId = packId,
        title = title,
        imageUrl = imageUrl,
        description = description,
        localPath = localPath,
    )
}

fun WidgetCategoryEntity.toDomain(widgetPacks: List<WidgetPack> = emptyList()): WidgetCategory {
    return WidgetCategory(
        id = id,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight,
        widgetPacks = widgetPacks
    )
}

fun WidgetCategory.toEntity(): WidgetCategoryEntity {
    return WidgetCategoryEntity(
        id = id,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight
    )
}

fun WidgetPackEntity.toDomain(
    widgets: List<Widget> = emptyList(),
    compatibleThemes: List<com.amobear.themepack.data.model.CompatibleTheme> = emptyList()
): WidgetPack {
    return WidgetPack(
        id = id,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        categoryId = categoryId,
        type = type,
        widgets = widgets,
        compatibleThemes = compatibleThemes,
        isPurchased = isPurchased,
        isUnlocked = isPurchased, // Derived from isPurchased
        localPath = null
    )
}

fun WidgetPack.toEntity(): WidgetPackEntity {
    return WidgetPackEntity(
        id = id,
        categoryId = categoryId,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        type = type,
        isPurchased = isPurchased
    )
}

fun WidgetEntity.toDomain(): Widget {
    return Widget(
        id = id,
        title = title,
        previewImage = previewImage,
        width = width,
        height = height,
        size = size,
        packId = packId,
        isInstalled = isInstalled,
        localPath = localPath,
        configuration = configuration
    )
}

fun Widget.toEntity(): WidgetEntity {
    return WidgetEntity(
        id = id,
        packId = packId,
        title = title,
        previewImage = previewImage,
        width = width,
        height = height,
        size = size,
        isInstalled = isInstalled,
        localPath = localPath,
        configuration = configuration
    )
}

fun IconCategoryEntity.toDomain(iconPacks: List<IconPack> = emptyList()): IconCategory {
    return IconCategory(
        id = id,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight,
        iconPacks = iconPacks
    )
}

fun IconCategory.toEntity(): IconCategoryEntity {
    return IconCategoryEntity(
        id = id,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight
    )
}

fun IconPackEntity.toDomain(
    icons: List<Icon> = emptyList(),
    compatibleThemes: List<com.amobear.themepack.data.model.CompatibleTheme> = emptyList()
): IconPack {
    return IconPack(
        id = id,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        categoryId = categoryId,
        icons = icons,
        compatibleThemes = compatibleThemes,
        isPurchased = isPurchased,
        isUnlocked = isPurchased, // Derived from isPurchased
        localPath = null
    )
}

fun IconPack.toEntity(): IconPackEntity {
    return IconPackEntity(
        id = id,
        categoryId = categoryId,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        isPurchased = isPurchased
    )
}

fun IconEntity.toDomain(): Icon {
    return Icon(
        id = id,
        appId = appId,
        name = name,
        imageUrl = imageUrl,
        iconPackId = iconPackId,
        localPath = localPath
    )
}

fun Icon.toEntity(): IconEntity {
    return IconEntity(
        id = id,
        iconPackId = iconPackId,
        appId = appId,
        name = name,
        imageUrl = imageUrl,
        localPath = localPath
    )
}
