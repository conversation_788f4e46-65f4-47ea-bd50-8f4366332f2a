package com.amobear.themepack.data.network

import com.amobear.themepack.data.model.ApiResponse
import com.amobear.themepack.data.model.ThemesApiResponseNoId
import com.amobear.themepack.data.model.WidgetPacksApiResponseNoId
import com.amobear.themepack.data.model.IconPacksApiResponseNoId
import retrofit2.Response
import retrofit2.http.GET

/**
 * API Service for endpoints that return nested objects without IDs
 * This represents the real API structure where objects don't have unique identifiers
 */
interface ApiServiceNoIds {

    /**
     * Get all theme categories and their themes without IDs
     * Endpoint: GET /api/themes
     */
    @GET("api/themes")
    suspend fun getThemes(): Response<ApiResponse<ThemesApiResponseNoId>>

    /**
     * Get all widget categories and their widget packs without IDs
     * Endpoint: GET /api/widget-packs
     */
    @GET("api/widget-packs")
    suspend fun getWidgetPacks(): Response<ApiResponse<WidgetPacksApiResponseNoId>>

    /**
     * Get all icon categories and their icon packs without IDs
     * Endpoint: GET /api/icon-packs
     */
    @GET("api/icon-packs")
    suspend fun getIconPacks(): Response<ApiResponse<IconPacksApiResponseNoId>>

    companion object {
        const val BASE_URL = "https://api.themepack.example.com/"
        
        // For development with mock JSON files
        const val MOCK_BASE_URL = "file:///android_asset/"
    }
}

/**
 * Object for API endpoints
 */
object ApiEndpointsNoIds {
    const val THEMES = "api/themes"
    const val WIDGET_PACKS = "api/widget-packs"
    const val ICON_PACKS = "api/icon-packs"
}
