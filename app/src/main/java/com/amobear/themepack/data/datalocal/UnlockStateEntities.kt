package com.amobear.themepack.data.datalocal

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Entity models for storing unlock states in Room database
 * Only unlocked items are stored - if not in database, it's locked
 */

@Entity(tableName = "unlocked_themes")
data class UnlockedThemeEntity(
    @PrimaryKey
    val themeId: Int,
    val categoryTitle: String,
    val themeTitle: String,
    val unlockedAt: Long = System.currentTimeMillis()
)

@Entity(tableName = "unlocked_wallpaper_packs")
data class UnlockedWallpaperPackEntity(
    @PrimaryKey
    val packId: Int,
    val themeTitle: String,
    val packTitle: String,
    val unlockedAt: Long = System.currentTimeMillis()
)

@Entity(tableName = "unlocked_widget_packs")
data class UnlockedWidgetPackEntity(
    @PrimaryKey
    val packId: Int,
    val themeTitle: String,
    val packTitle: String,
    val unlockedAt: Long = System.currentTimeMillis()
)

@Entity(tableName = "unlocked_icon_packs")
data class UnlockedIconPackEntity(
    @PrimaryKey
    val packId: Int,
    val themeTitle: String,
    val packTitle: String,
    val unlockedAt: Long = System.currentTimeMillis()
)

@Entity(tableName = "unlocked_wallpapers")
data class UnlockedWallpaperEntity(
    @PrimaryKey
    val wallpaperId: Int,
    val wallpaperUrl: String,
    val wallpaperTitle: String,
    val packTitle: String,
    val unlockedAt: Long = System.currentTimeMillis()
)

/**
 * For composite key unlocks (when we need to track by title combinations)
 */
@Entity(tableName = "unlocked_themes_composite")
data class UnlockedThemeCompositeEntity(
    @PrimaryKey
    val compositeKey: String, // "categoryTitle|themeTitle"
    val categoryTitle: String,
    val themeTitle: String,
    val unlockedAt: Long = System.currentTimeMillis()
)

@Entity(tableName = "unlocked_packs_composite")
data class UnlockedPackCompositeEntity(
    @PrimaryKey
    val compositeKey: String, // "themeTitle|packTitle|packType"
    val themeTitle: String,
    val packTitle: String,
    val packType: String, // "wallpaper", "widget", "icon"
    val unlockedAt: Long = System.currentTimeMillis()
)

@Entity(tableName = "unlocked_wallpapers_by_url")
data class UnlockedWallpaperByUrlEntity(
    @PrimaryKey
    val wallpaperUrl: String,
    val wallpaperTitle: String? = null,
    val unlockedAt: Long = System.currentTimeMillis()
)
