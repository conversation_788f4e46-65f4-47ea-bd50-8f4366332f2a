package com.amobear.themepack.data.model

import com.squareup.moshi.JsonClass

/**
 * API Models for responses that don't have IDs - representing the real API structure
 * These models match the actual API response format where nested objects don't have unique IDs
 */

// ============= THEMES API MODELS WITHOUT IDS =============

@JsonClass(generateAdapter = true)
data class ApiThemeCategoryNoId(
    val title: String,
    val titleIconUrl: String,
    val weight: Int,
    val themes: List<ApiThemeNoId>
)

@JsonClass(generateAdapter = true)
data class ApiThemeNoId(
    val title: String,
    val description: String,
    val previewImage: String,
    val wallpaperPacks: List<ApiWallpaperPackNoId>,
    val widgetPacks: List<ApiWidgetPackNoId>,
    val iconPacks: List<ApiIconPackNoId>
)

@JsonClass(generateAdapter = true)
data class ApiWallpaperPackNoId(
    val title: String,
    val previewImage: String,
    val weight: Int,
    val coin: Int,
    val wallpapers: List<ApiWallpaperNoId>
)

@JsonClass(generateAdapter = true)
data class ApiWallpaperNoId(
    val title: String,
    val imageUrl: String,
    val description: String
)

// ============= WIDGET PACKS API MODELS WITHOUT IDS =============

@JsonClass(generateAdapter = true)
data class ApiWidgetCategoryNoId(
    val title: String,
    val titleIconUrl: String,
    val weight: Int,
    val widgetPacks: List<ApiWidgetPackNoId>
)

@JsonClass(generateAdapter = true)
data class ApiWidgetPackNoId(
    val title: String,
    val previewImage: String,
    val weight: Int,
    val coin: Int,
    val type: String, // "clock", "weather", "calendar", "frame"
    val widgets: List<ApiWidgetNoId>,
    val compatibleThemes: List<ApiCompatibleThemeNoId> = emptyList()
)

@JsonClass(generateAdapter = true)
data class ApiWidgetNoId(
    val title: String,
    val previewImage: String,
    val width: Int,
    val height: Int,
    val size: String // "small", "medium", "large"
)

@JsonClass(generateAdapter = true)
data class ApiCompatibleThemeNoId(
    val title: String
)

// ============= ICON PACKS API MODELS WITHOUT IDS =============

@JsonClass(generateAdapter = true)
data class ApiIconCategoryNoId(
    val title: String,
    val titleIconUrl: String,
    val weight: Int,
    val iconPacks: List<ApiIconPackNoId>
)

@JsonClass(generateAdapter = true)
data class ApiIconPackNoId(
    val title: String,
    val previewImage: String,
    val weight: Int,
    val coin: Int,
    val icons: List<ApiIconNoId>,
    val compatibleThemes: List<ApiCompatibleThemeNoId> = emptyList()
)

@JsonClass(generateAdapter = true)
data class ApiIconNoId(
    val appId: String,
    val name: String,
    val imageUrl: String
)

// ============= RESPONSE WRAPPERS =============

@JsonClass(generateAdapter = true)
data class ThemesApiResponseNoId(
    val categories: List<ApiThemeCategoryNoId>
)

@JsonClass(generateAdapter = true)
data class WidgetPacksApiResponseNoId(
    val categories: List<ApiWidgetCategoryNoId>
)

@JsonClass(generateAdapter = true)
data class IconPacksApiResponseNoId(
    val categories: List<ApiIconCategoryNoId>
)

// ============= MAPPING EXTENSIONS WITH ID GENERATION =============

/**
 * Extension functions to convert API models without IDs to domain models with generated IDs
 * Uses composite keys and hash codes to generate stable, unique IDs
 */

fun ApiThemeCategoryNoId.toThemeCategory(categoryIndex: Int): ThemeCategory {
    val categoryId = generateCategoryId(title, categoryIndex)
    return ThemeCategory(
        id = categoryId,
        title = title,
        titleIconUrl = titleIconUrl,
        weight = weight,
        themes = themes.mapIndexed { themeIndex, theme -> 
            theme.toTheme(categoryId, themeIndex) 
        }
    )
}

fun ApiThemeNoId.toTheme(categoryId: Int, themeIndex: Int): Theme {
    val themeId = generateThemeId(categoryId, title, themeIndex)
    return Theme(
        id = themeId,
        title = title,
        description = description,
        previewImage = previewImage,
        categoryId = categoryId,
        wallpaperPacks = wallpaperPacks.mapIndexed { packIndex, pack -> 
            pack.toWallpaperPack(themeId, packIndex) 
        },
        widgetPacks = widgetPacks.mapIndexed { packIndex, pack -> 
            pack.toWidgetPack(themeId, packIndex) 
        },
        iconPacks = iconPacks.mapIndexed { packIndex, pack -> 
            pack.toIconPack(themeId, packIndex) 
        },
        // Default stateless values
        downloadDate = null,
        isUnlocked = false,
        localPath = null,
        isFromCache = false,
        matchConfidence = 1.0f
    )
}

fun ApiWallpaperPackNoId.toWallpaperPack(themeId: Int, packIndex: Int): WallpaperPack {
    val packId = generateWallpaperPackId(themeId, title, packIndex)
    return WallpaperPack(
        id = packId,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        wallpapers = wallpapers.mapIndexed { wallpaperIndex, wallpaper -> 
            wallpaper.toWallpaper(packId, wallpaperIndex) 
        },
        isPurchased = false
    )
}

fun ApiWallpaperNoId.toWallpaper(packId: Int, wallpaperIndex: Int): Wallpaper {
    val wallpaperId = generateWallpaperId(packId, title, wallpaperIndex)
    return Wallpaper(
        id = wallpaperId,
        title = title,
        imageUrl = imageUrl,
        description = description,
        packId = packId,
        isUnlocked = false,
        localPath = null
    )
}

fun ApiWidgetPackNoId.toWidgetPack(themeId: Int, packIndex: Int): WidgetPack {
    val packId = generateWidgetPackId(themeId, title, packIndex)
    return WidgetPack(
        id = packId,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        categoryId = themeId, // Using themeId as categoryId for widgets in themes
        type = WidgetType.fromString(type),
        widgets = widgets.mapIndexed { widgetIndex, widget -> 
            widget.toWidget(packId, widgetIndex) 
        },
        compatibleThemes = compatibleThemes.mapIndexed { themeIndex, theme -> 
            theme.toCompatibleTheme(themeIndex) 
        },
        isPurchased = false
    )
}

fun ApiWidgetNoId.toWidget(packId: Int, widgetIndex: Int): Widget {
    val widgetId = generateWidgetId(packId, title, widgetIndex)
    return Widget(
        id = widgetId,
        title = title,
        previewImage = previewImage,
        width = width,
        height = height,
        size = WidgetSize.fromString(size),
        packId = packId
    )
}

fun ApiIconPackNoId.toIconPack(themeId: Int, packIndex: Int): IconPack {
    val packId = generateIconPackId(themeId, title, packIndex)
    return IconPack(
        id = packId,
        title = title,
        previewImage = previewImage,
        weight = weight,
        coin = coin,
        categoryId = themeId, // Using themeId as categoryId for icons in themes
        icons = icons.mapIndexed { iconIndex, icon -> 
            icon.toIcon(packId, iconIndex) 
        },
        compatibleThemes = compatibleThemes.mapIndexed { themeIndex, theme -> 
            theme.toCompatibleTheme(themeIndex) 
        },
        isPurchased = false,
        isUnlocked = false,
        localPath = null
    )
}

fun ApiIconNoId.toIcon(packId: Int, iconIndex: Int): Icon {
    val iconId = generateIconId(packId, appId, iconIndex)
    return Icon(
        id = iconId,
        appId = appId,
        name = name,
        imageUrl = imageUrl,
        iconPackId = packId,
        localPath = null
    )
}

fun ApiCompatibleThemeNoId.toCompatibleTheme(themeIndex: Int): CompatibleTheme {
    val themeId = generateCompatibleThemeId(title, themeIndex)
    return CompatibleTheme(
        id = themeId,
        title = title
    )
}

// ============= ID GENERATION FUNCTIONS =============

/**
 * Generate stable, unique IDs using hash codes of composite keys
 * These IDs will be consistent across app sessions for the same data
 */

private fun generateCategoryId(title: String, index: Int): Int {
    return "category_${title}_$index".hashCode().let { 
        if (it < 0) -it else it 
    }
}

private fun generateThemeId(categoryId: Int, title: String, index: Int): Int {
    return "theme_${categoryId}_${title}_$index".hashCode().let { 
        if (it < 0) -it else it 
    }
}

private fun generateWallpaperPackId(themeId: Int, title: String, index: Int): Int {
    return "wallpaper_pack_${themeId}_${title}_$index".hashCode().let { 
        if (it < 0) -it else it 
    }
}

private fun generateWallpaperId(packId: Int, title: String, index: Int): Int {
    return "wallpaper_${packId}_${title}_$index".hashCode().let { 
        if (it < 0) -it else it 
    }
}

private fun generateWidgetPackId(themeId: Int, title: String, index: Int): Int {
    return "widget_pack_${themeId}_${title}_$index".hashCode().let { 
        if (it < 0) -it else it 
    }
}

private fun generateWidgetId(packId: Int, title: String, index: Int): Int {
    return "widget_${packId}_${title}_$index".hashCode().let { 
        if (it < 0) -it else it 
    }
}

private fun generateIconPackId(themeId: Int, title: String, index: Int): Int {
    return "icon_pack_${themeId}_${title}_$index".hashCode().let { 
        if (it < 0) -it else it 
    }
}

private fun generateIconId(packId: Int, appId: String, index: Int): Int {
    return "icon_${packId}_${appId}_$index".hashCode().let { 
        if (it < 0) -it else it 
    }
}

private fun generateCompatibleThemeId(title: String, index: Int): Int {
    return "compatible_theme_${title}_$index".hashCode().let { 
        if (it < 0) -it else it 
    }
}
