package com.amobear.themepack.data.repository

import com.amobear.themepack.data.datalocal.ThemeAppDatabase
import com.amobear.themepack.data.mapper.toDomain
import com.amobear.themepack.data.mapper.toEntity
import com.amobear.themepack.data.model.IconCategory
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.ThemeCategory
import com.amobear.themepack.data.model.WidgetCategory
import com.amobear.themepack.data.model.toIconPack
import com.amobear.themepack.data.model.toThemeCategory
import com.amobear.themepack.data.model.toWidgetPack
import com.amobear.themepack.data.network.ApiResult
import com.amobear.themepack.data.network.ApiService
import com.amobear.themepack.data.network.toApiResult
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Stateless repository - only handles pure data fetching without unlock status
 * All data returned here has no unlock/purchase state
 */
@Singleton
class StatelessThemeRepository @Inject constructor(
    private val apiService: ApiService,
    private val themeAppDatabase: ThemeAppDatabase,
) {

    /**
     * Get all theme categories from cache (Room database)
     * Returns pure data without unlock status
     */
    val themeCategories: Flow<List<ThemeCategory>> =
        themeAppDatabase.themeCategoryDao().getAllCategories().map { entities ->
            entities.map { it.toDomain() }
        }

    val widgetCategories: Flow<List<WidgetCategory>> = 
        themeAppDatabase.widgetCategoryDao().getAllWidgetCategories().map { entities ->
            entities.map { it.toDomain() }
        }

    val iconCategories: Flow<List<IconCategory>> =
        themeAppDatabase.iconCategoryDao().getAllIconCategories().map { entities ->
            entities.map { it.toDomain() }
        }

    /**
     * Fetch theme categories from API and cache to database
     * Returns pure data without any unlock status
     * @param forceRefresh Force refresh from network
     */
    suspend fun fetchThemeCategories(forceRefresh: Boolean = false): ApiResult<List<ThemeCategory>> {
        return try {
            // Check if we should use cache
            if (!forceRefresh) {
                val cachedCategories = themeAppDatabase.themeCategoryDao().getAllCategoriesSync()
                if (cachedCategories.isNotEmpty()) {
                    return ApiResult.Success(cachedCategories.map { it.toDomain() })
                }
            }

            // Fetch from API
            val response = apiService.getThemes().toApiResult()

            when (response) {
                is ApiResult.Success -> {
                    // Convert API response to domain models (without unlock status)
                    val themeCategories = response.data.data.categories.map { apiCategory ->
                        ThemeCategory(
                            id = apiCategory.id,
                            title = apiCategory.title,
                            titleIconUrl = apiCategory.titleIconUrl,
                            weight = apiCategory.weight,
                            themes = apiCategory.themes.map { apiTheme ->
                                Theme(
                                    id = apiTheme.id,
                                    title = apiTheme.title,
                                    description = apiTheme.description,
                                    previewImage = apiTheme.previewImage,
                                    categoryId = apiTheme.categoryId,
                                    wallpaperPacks = apiTheme.wallpaperPacks.map { it.toWallpaperPack() },
                                    widgetPacks = apiTheme.widgetPacks.map { it.toWidgetPack() },
                                    iconPacks = apiTheme.iconPacks.map { it.toIconPack() },
                                    // Pure stateless data - no unlock status
                                    downloadDate = null,
                                    isUnlocked = false, // Always false in stateless repo
                                    localPath = null,
                                    isFromCache = !forceRefresh,
                                    matchConfidence = 1.0f
                                )
                            }
                        )
                    }

                    // Cache to database
                    themeAppDatabase.themeCategoryDao().deleteAllCategories()
                    themeAppDatabase.themeCategoryDao()
                        .insertCategories(themeCategories.map { it.toEntity() })

                    ApiResult.Success(themeCategories)
                }
                is ApiResult.Error -> response
                is ApiResult.Loading -> response
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    /**
     * Fetch widget categories from API and cache to database
     */
    suspend fun fetchWidgetCategories(forceRefresh: Boolean = false): ApiResult<List<WidgetCategory>> {
        return try {
            if (!forceRefresh) {
                val cachedCategories = themeAppDatabase.widgetCategoryDao().getAllWidgetCategoriesSync()
                if (cachedCategories.isNotEmpty()) {
                    return ApiResult.Success(cachedCategories.map { it.toDomain() })
                }
            }

            val response = apiService.getWidgetPacks().toApiResult()

            when (response) {
                is ApiResult.Success -> {
                    val widgetCategories = response.data.data.categories.map { apiCategory ->
                        WidgetCategory(
                            id = apiCategory.id,
                            title = apiCategory.title,
                            titleIconUrl = apiCategory.titleIconUrl,
                            weight = apiCategory.weight,
                            widgetPacks = apiCategory.widgetPacks.map { it.toWidgetPack() }
                        )
                    }

                    themeAppDatabase.widgetCategoryDao().deleteAllWidgetCategories()
                    themeAppDatabase.widgetCategoryDao()
                        .insertWidgetCategories(widgetCategories.map { it.toEntity() })

                    ApiResult.Success(widgetCategories)
                }
                is ApiResult.Error -> response
                is ApiResult.Loading -> response
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    /**
     * Fetch icon categories from API and cache to database
     */
    suspend fun fetchIconCategories(forceRefresh: Boolean = false): ApiResult<List<IconCategory>> {
        return try {
            if (!forceRefresh) {
                val cachedCategories = themeAppDatabase.iconCategoryDao().getAllIconCategoriesSync()
                if (cachedCategories.isNotEmpty()) {
                    return ApiResult.Success(cachedCategories.map { it.toDomain() })
                }
            }

            val response = apiService.getIconPacks().toApiResult()

            when (response) {
                is ApiResult.Success -> {
                    val iconCategories = response.data.data.categories.map { apiCategory ->
                        IconCategory(
                            id = apiCategory.id,
                            title = apiCategory.title,
                            titleIconUrl = apiCategory.titleIconUrl,
                            weight = apiCategory.weight,
                            iconPacks = apiCategory.iconPacks.map { it.toIconPack() }
                        )
                    }

                    themeAppDatabase.iconCategoryDao().deleteAllIconCategories()
                    themeAppDatabase.iconCategoryDao()
                        .insertIconCategories(iconCategories.map { it.toEntity() })

                    ApiResult.Success(iconCategories)
                }
                is ApiResult.Error -> response
                is ApiResult.Loading -> response
            }
        } catch (e: Exception) {
            ApiResult.Error(e)
        }
    }

    /**
     * Get theme by ID (stateless)
     */
    fun getThemeById(themeId: Int): Flow<Theme?> = flow {
        val theme = themeAppDatabase.themeDao().getThemeById(themeId)?.toDomain()
        emit(theme)
    }

    /**
     * Get themes by category ID (stateless)
     */
    fun getThemesByCategoryId(categoryId: Int): Flow<List<Theme>> =
        themeAppDatabase.themeDao().getThemesByCategory(categoryId).map { entities ->
            entities.map { it.toDomain() }
        }
}
