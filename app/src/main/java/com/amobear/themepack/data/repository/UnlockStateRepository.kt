package com.amobear.themepack.data.repository

import com.amobear.themepack.data.datalocal.ThemeAppDatabase
import com.amobear.themepack.data.model.PackType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing unlock/purchase state using existing Entity models
 * Uses database entities to track unlock status - if entity has unlock state = true, it's unlocked
 */
@Singleton
class UnlockStateRepository @Inject constructor(
    private val database: ThemeAppDatabase
) {
    
    /**
     * Get unlocked theme IDs from database
     */
    val unlockedThemes: Flow<Set<Int>> =
        database.themeDao().getAllThemes().map { themes ->
            themes.filter { it.downloadDate != null }.map { it.id }.toSet()
        }

    /**
     * Get unlocked wallpaper pack IDs from database
     */
    val unlockedWallpaperPacks: Flow<Set<Int>> =
        database.wallpaperDao().getAllWallpaperPacks().map { packs ->
            packs.filter { it.isPurchased }.map { it.id }.toSet()
        }

    /**
     * Get unlocked widget pack IDs from database
     */
    val unlockedWidgetPacks: Flow<Set<Int>> =
        database.widgetPackDao().getAllWidgetPacks().map { packs ->
            packs.filter { it.isPurchased }.map { it.id }.toSet()
        }

    /**
     * Get unlocked icon pack IDs from database
     */
    val unlockedIconPacks: Flow<Set<Int>> =
        database.iconPackDao().getAllIconPacks().map { packs ->
            packs.filter { it.isPurchased }.map { it.id }.toSet()
        }

    /**
     * Get unlocked wallpaper URLs from database
     */
    val unlockedWallpapers: Flow<Set<String>> =
        database.wallpaperDao().getAllWallpapers().map { wallpapers ->
            wallpapers.filter { it.localPath != null }.map { it.imageUrl }.toSet()
        }

    // ============= THEME UNLOCK METHODS =============

    /**
     * Check if a theme is unlocked
     */
    suspend fun isThemeUnlocked(themeId: Int): Boolean {
        val theme = database.themeDao().getThemeById(themeId)
        return theme?.downloadDate != null
    }

    /**
     * Unlock a theme
     */
    suspend fun unlockTheme(themeId: Int) {
        val theme = database.themeDao().getThemeById(themeId)
        theme?.let {
            val updatedTheme = it.copy(downloadDate = System.currentTimeMillis())
            database.themeDao().updateTheme(updatedTheme)
        }
    }

    // ============= WALLPAPER PACK UNLOCK METHODS =============

    /**
     * Check if a wallpaper pack is unlocked
     */
    suspend fun isWallpaperPackUnlocked(packId: Int): Boolean {
        val pack = database.wallpaperDao().getWallpaperPackById(packId)
        return pack?.isPurchased == true
    }

    /**
     * Unlock a wallpaper pack
     */
    suspend fun unlockWallpaperPack(packId: Int) {
        val pack = database.wallpaperDao().getWallpaperPackById(packId)
        pack?.let {
            val updatedPack = it.copy(isPurchased = true)
            database.wallpaperDao().updateWallpaperPack(updatedPack)
        }
    }

    // ============= WIDGET PACK UNLOCK METHODS =============

    /**
     * Check if a widget pack is unlocked
     */
    suspend fun isWidgetPackUnlocked(packId: Int): Boolean {
        val pack = database.widgetPackDao().getWidgetPackById(packId)
        return pack?.isPurchased == true
    }

    /**
     * Unlock a widget pack
     */
    suspend fun unlockWidgetPack(packId: Int) {
        val pack = database.widgetPackDao().getWidgetPackById(packId)
        pack?.let {
            val updatedPack = it.copy(isPurchased = true)
            database.widgetPackDao().updateWidgetPack(updatedPack)
        }
    }

    // ============= ICON PACK UNLOCK METHODS =============

    /**
     * Check if an icon pack is unlocked
     */
    suspend fun isIconPackUnlocked(packId: Int): Boolean {
        val pack = database.iconPackDao().getIconPackById(packId)
        return pack?.isPurchased == true
    }

    /**
     * Unlock an icon pack
     */
    suspend fun unlockIconPack(packId: Int) {
        val pack = database.iconPackDao().getIconPackById(packId)
        pack?.let {
            val updatedPack = it.copy(isPurchased = true)
            database.iconPackDao().updateIconPack(updatedPack)
        }
    }

    // ============= INDIVIDUAL WALLPAPER UNLOCK METHODS =============

    /**
     * Check if a specific wallpaper is unlocked by URL
     */
    suspend fun isWallpaperUnlocked(wallpaperUrl: String): Boolean {
        // We need to get wallpaper by URL from database
        // This is a simplified approach - in real app you might want to add a query method
        return false // TODO: Implement proper wallpaper unlock check by URL
    }

    /**
     * Unlock a specific wallpaper by URL
     */
    suspend fun unlockWallpaper(wallpaperUrl: String) {
        // TODO: Implement proper wallpaper unlock by URL
        // This would require finding wallpaper by URL and updating its localPath
    }

    // ============= GENERIC UNLOCK METHODS =============

    /**
     * Unlock any pack by type and ID
     */
    suspend fun unlockPack(packType: PackType, packId: Int) {
        when (packType) {
            PackType.WALLPAPER -> unlockWallpaperPack(packId)
            PackType.WIDGET -> unlockWidgetPack(packId)
            PackType.ICON -> unlockIconPack(packId)
        }
    }

    /**
     * Check if any pack is unlocked by type and ID
     */
    suspend fun isPackUnlocked(packType: PackType, packId: Int): Boolean {
        return when (packType) {
            PackType.WALLPAPER -> isWallpaperPackUnlocked(packId)
            PackType.WIDGET -> isWidgetPackUnlocked(packId)
            PackType.ICON -> isIconPackUnlocked(packId)
        }
    }

}
