package com.amobear.themepack.data.repository

import com.amobear.themepack.data.datalocal.sharepref.SharePreferenceProvider
import com.amobear.themepack.data.model.PackType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.collections.immutable.ImmutableSet
import kotlinx.collections.immutable.persistentSetOf
import kotlinx.collections.immutable.toImmutableSet
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing unlock/purchase state of themes, wallpapers, widgets, and icons
 * This is the only place where unlock status is stored and managed
 */
@Singleton
class UnlockStateRepository @Inject constructor(
    private val preferenceProvider: SharePreferenceProvider
) {
    
    companion object {
        private const val KEY_UNLOCKED_THEMES = "unlocked_themes"
        private const val KEY_UNLOCKED_WALLPAPER_PACKS = "unlocked_wallpaper_packs"
        private const val KEY_UNLOCKED_WIDGET_PACKS = "unlocked_widget_packs"
        private const val KEY_UNLOCKED_ICON_PACKS = "unlocked_icon_packs"
        private const val KEY_UNLOCKED_WALLPAPERS = "unlocked_wallpapers"
        private const val SEPARATOR = ";"
    }

    // In-memory cache for unlock states
    private val _unlockedThemes = MutableStateFlow<ImmutableSet<Int>>(persistentSetOf())
    private val _unlockedWallpaperPacks = MutableStateFlow<ImmutableSet<Int>>(persistentSetOf())
    private val _unlockedWidgetPacks = MutableStateFlow<ImmutableSet<Int>>(persistentSetOf())
    private val _unlockedIconPacks = MutableStateFlow<ImmutableSet<Int>>(persistentSetOf())
    private val _unlockedWallpapers = MutableStateFlow<ImmutableSet<String>>(persistentSetOf())

    val unlockedThemes: Flow<ImmutableSet<Int>> = _unlockedThemes.asStateFlow()
    val unlockedWallpaperPacks: Flow<ImmutableSet<Int>> = _unlockedWallpaperPacks.asStateFlow()
    val unlockedWidgetPacks: Flow<ImmutableSet<Int>> = _unlockedWidgetPacks.asStateFlow()
    val unlockedIconPacks: Flow<ImmutableSet<Int>> = _unlockedIconPacks.asStateFlow()
    val unlockedWallpapers: Flow<ImmutableSet<String>> = _unlockedWallpapers.asStateFlow()

    init {
        // Load unlock states from SharedPreferences on initialization
        loadUnlockStates()
    }

    /**
     * Load all unlock states from SharedPreferences
     */
    private fun loadUnlockStates() {
        _unlockedThemes.value = loadIntSet(KEY_UNLOCKED_THEMES)
        _unlockedWallpaperPacks.value = loadIntSet(KEY_UNLOCKED_WALLPAPER_PACKS)
        _unlockedWidgetPacks.value = loadIntSet(KEY_UNLOCKED_WIDGET_PACKS)
        _unlockedIconPacks.value = loadIntSet(KEY_UNLOCKED_ICON_PACKS)
        _unlockedWallpapers.value = loadStringSet(KEY_UNLOCKED_WALLPAPERS)
    }

    /**
     * Helper function to load integer set from SharedPreferences
     */
    private fun loadIntSet(key: String): ImmutableSet<Int> {
        val savedString = preferenceProvider.get<String>(key) ?: ""
        return if (savedString.isNotEmpty()) {
            savedString.split(SEPARATOR)
                .mapNotNull { it.toIntOrNull() }
                .toImmutableSet()
        } else {
            persistentSetOf()
        }
    }

    /**
     * Helper function to load string set from SharedPreferences
     */
    private fun loadStringSet(key: String): ImmutableSet<String> {
        val savedString = preferenceProvider.get<String>(key) ?: ""
        return if (savedString.isNotEmpty()) {
            savedString.split(SEPARATOR).toImmutableSet()
        } else {
            persistentSetOf()
        }
    }

    /**
     * Helper function to save integer set to SharedPreferences
     */
    private fun saveIntSet(key: String, set: ImmutableSet<Int>) {
        val stringValue = set.joinToString(SEPARATOR)
        preferenceProvider.save<String>(key, stringValue)
    }

    /**
     * Helper function to save string set to SharedPreferences
     */
    private fun saveStringSet(key: String, set: ImmutableSet<String>) {
        val stringValue = set.joinToString(SEPARATOR)
        preferenceProvider.save<String>(key, stringValue)
    }

    // ============= THEME UNLOCK METHODS =============

    /**
     * Check if a theme is unlocked
     */
    fun isThemeUnlocked(themeId: Int): Boolean {
        return _unlockedThemes.value.contains(themeId)
    }

    /**
     * Unlock a theme
     */
    suspend fun unlockTheme(themeId: Int) {
        _unlockedThemes.update { currentSet ->
            val newSet = currentSet.add(themeId)
            saveIntSet(KEY_UNLOCKED_THEMES, newSet)
            newSet
        }
    }

    /**
     * Lock a theme (for testing purposes)
     */
    suspend fun lockTheme(themeId: Int) {
        _unlockedThemes.update { currentSet ->
            val newSet = currentSet.remove(themeId)
            saveIntSet(KEY_UNLOCKED_THEMES, newSet)
            newSet
        }
    }

    // ============= WALLPAPER PACK UNLOCK METHODS =============

    /**
     * Check if a wallpaper pack is unlocked
     */
    fun isWallpaperPackUnlocked(packId: Int): Boolean {
        return _unlockedWallpaperPacks.value.contains(packId)
    }

    /**
     * Unlock a wallpaper pack
     */
    suspend fun unlockWallpaperPack(packId: Int) {
        _unlockedWallpaperPacks.update { currentSet ->
            val newSet = currentSet.add(packId)
            saveIntSet(KEY_UNLOCKED_WALLPAPER_PACKS, newSet)
            newSet
        }
    }

    // ============= WIDGET PACK UNLOCK METHODS =============

    /**
     * Check if a widget pack is unlocked
     */
    fun isWidgetPackUnlocked(packId: Int): Boolean {
        return _unlockedWidgetPacks.value.contains(packId)
    }

    /**
     * Unlock a widget pack
     */
    suspend fun unlockWidgetPack(packId: Int) {
        _unlockedWidgetPacks.update { currentSet ->
            val newSet = currentSet.add(packId)
            saveIntSet(KEY_UNLOCKED_WIDGET_PACKS, newSet)
            newSet
        }
    }

    // ============= ICON PACK UNLOCK METHODS =============

    /**
     * Check if an icon pack is unlocked
     */
    fun isIconPackUnlocked(packId: Int): Boolean {
        return _unlockedIconPacks.value.contains(packId)
    }

    /**
     * Unlock an icon pack
     */
    suspend fun unlockIconPack(packId: Int) {
        _unlockedIconPacks.update { currentSet ->
            val newSet = currentSet.add(packId)
            saveIntSet(KEY_UNLOCKED_ICON_PACKS, newSet)
            newSet
        }
    }

    // ============= INDIVIDUAL WALLPAPER UNLOCK METHODS =============

    /**
     * Check if a specific wallpaper is unlocked
     */
    fun isWallpaperUnlocked(wallpaperUrl: String): Boolean {
        return _unlockedWallpapers.value.contains(wallpaperUrl)
    }

    /**
     * Unlock a specific wallpaper
     */
    suspend fun unlockWallpaper(wallpaperUrl: String) {
        _unlockedWallpapers.update { currentSet ->
            val newSet = currentSet.add(wallpaperUrl)
            saveStringSet(KEY_UNLOCKED_WALLPAPERS, newSet)
            newSet
        }
    }

    // ============= GENERIC UNLOCK METHODS =============

    /**
     * Unlock any pack by type and ID
     */
    suspend fun unlockPack(packType: PackType, packId: Int) {
        when (packType) {
            PackType.WALLPAPER -> unlockWallpaperPack(packId)
            PackType.WIDGET -> unlockWidgetPack(packId)
            PackType.ICON -> unlockIconPack(packId)
        }
    }

    /**
     * Check if any pack is unlocked by type and ID
     */
    fun isPackUnlocked(packType: PackType, packId: Int): Boolean {
        return when (packType) {
            PackType.WALLPAPER -> isWallpaperPackUnlocked(packId)
            PackType.WIDGET -> isWidgetPackUnlocked(packId)
            PackType.ICON -> isIconPackUnlocked(packId)
        }
    }

    // ============= BULK OPERATIONS =============

    /**
     * Get all unlocked items as a summary
     */
    fun getUnlockSummary(): Flow<UnlockSummary> = flow {
        emit(
            UnlockSummary(
                unlockedThemes = _unlockedThemes.value,
                unlockedWallpaperPacks = _unlockedWallpaperPacks.value,
                unlockedWidgetPacks = _unlockedWidgetPacks.value,
                unlockedIconPacks = _unlockedIconPacks.value,
                unlockedWallpapers = _unlockedWallpapers.value
            )
        )
    }

    /**
     * Clear all unlock states (for testing or reset purposes)
     */
    suspend fun clearAllUnlockStates() {
        _unlockedThemes.value = persistentSetOf()
        _unlockedWallpaperPacks.value = persistentSetOf()
        _unlockedWidgetPacks.value = persistentSetOf()
        _unlockedIconPacks.value = persistentSetOf()
        _unlockedWallpapers.value = persistentSetOf()

        preferenceProvider.save<String>(KEY_UNLOCKED_THEMES, "")
        preferenceProvider.save<String>(KEY_UNLOCKED_WALLPAPER_PACKS, "")
        preferenceProvider.save<String>(KEY_UNLOCKED_WIDGET_PACKS, "")
        preferenceProvider.save<String>(KEY_UNLOCKED_ICON_PACKS, "")
        preferenceProvider.save<String>(KEY_UNLOCKED_WALLPAPERS, "")
    }
}

/**
 * Data class representing the summary of all unlock states
 */
data class UnlockSummary(
    val unlockedThemes: ImmutableSet<Int>,
    val unlockedWallpaperPacks: ImmutableSet<Int>,
    val unlockedWidgetPacks: ImmutableSet<Int>,
    val unlockedIconPacks: ImmutableSet<Int>,
    val unlockedWallpapers: ImmutableSet<String>
)
