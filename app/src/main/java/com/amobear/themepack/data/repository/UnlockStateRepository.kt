package com.amobear.themepack.data.repository

import com.amobear.themepack.data.datalocal.ThemeAppDatabase
import com.amobear.themepack.data.model.PackType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing unlock/purchase state using existing Entity models
 * Uses database entities to track unlock status - if entity has unlock state = true, it's unlocked
 */
@Singleton
class UnlockStateRepository @Inject constructor(
    private val database: ThemeAppDatabase
) {
    
    companion object {
        private const val KEY_UNLOCKED_THEMES = "unlocked_themes"
        private const val KEY_UNLOCKED_WALLPAPER_PACKS = "unlocked_wallpaper_packs"
        private const val KEY_UNLOCKED_WIDGET_PACKS = "unlocked_widget_packs"
        private const val KEY_UNLOCKED_ICON_PACKS = "unlocked_icon_packs"
        private const val KEY_UNLOCKED_WALLPAPERS = "unlocked_wallpapers"

        // For composite key unlocks (when API doesn't provide IDs)
        private const val KEY_UNLOCKED_THEMES_BY_COMPOSITE = "unlocked_themes_composite"
        private const val KEY_UNLOCKED_PACKS_BY_COMPOSITE = "unlocked_packs_composite"
        private const val KEY_UNLOCKED_WALLPAPERS_BY_URL = "unlocked_wallpapers_url"

        private const val SEPARATOR = ";"
        private const val COMPOSITE_SEPARATOR = "|"
    }

    // In-memory cache for unlock states (ID-based)
    private val _unlockedThemes = MutableStateFlow<ImmutableSet<Int>>(persistentSetOf())
    private val _unlockedWallpaperPacks = MutableStateFlow<ImmutableSet<Int>>(persistentSetOf())
    private val _unlockedWidgetPacks = MutableStateFlow<ImmutableSet<Int>>(persistentSetOf())
    private val _unlockedIconPacks = MutableStateFlow<ImmutableSet<Int>>(persistentSetOf())
    private val _unlockedWallpapers = MutableStateFlow<ImmutableSet<String>>(persistentSetOf())

    // In-memory cache for composite key unlock states (for APIs without IDs)
    private val _unlockedThemesByComposite = MutableStateFlow<ImmutableSet<String>>(persistentSetOf())
    private val _unlockedPacksByComposite = MutableStateFlow<ImmutableSet<String>>(persistentSetOf())
    private val _unlockedWallpapersByUrl = MutableStateFlow<ImmutableSet<String>>(persistentSetOf())

    val unlockedThemes: Flow<ImmutableSet<Int>> = _unlockedThemes.asStateFlow()
    val unlockedWallpaperPacks: Flow<ImmutableSet<Int>> = _unlockedWallpaperPacks.asStateFlow()
    val unlockedWidgetPacks: Flow<ImmutableSet<Int>> = _unlockedWidgetPacks.asStateFlow()
    val unlockedIconPacks: Flow<ImmutableSet<Int>> = _unlockedIconPacks.asStateFlow()
    val unlockedWallpapers: Flow<ImmutableSet<String>> = _unlockedWallpapers.asStateFlow()

    // Composite key flows
    val unlockedThemesByComposite: Flow<ImmutableSet<String>> = _unlockedThemesByComposite.asStateFlow()
    val unlockedPacksByComposite: Flow<ImmutableSet<String>> = _unlockedPacksByComposite.asStateFlow()
    val unlockedWallpapersByUrl: Flow<ImmutableSet<String>> = _unlockedWallpapersByUrl.asStateFlow()

    init {
        // Load unlock states from SharedPreferences on initialization
        loadUnlockStates()
    }

    /**
     * Load all unlock states from SharedPreferences
     */
    private fun loadUnlockStates() {
        // Load ID-based unlock states
        _unlockedThemes.value = loadIntSet(KEY_UNLOCKED_THEMES)
        _unlockedWallpaperPacks.value = loadIntSet(KEY_UNLOCKED_WALLPAPER_PACKS)
        _unlockedWidgetPacks.value = loadIntSet(KEY_UNLOCKED_WIDGET_PACKS)
        _unlockedIconPacks.value = loadIntSet(KEY_UNLOCKED_ICON_PACKS)
        _unlockedWallpapers.value = loadStringSet(KEY_UNLOCKED_WALLPAPERS)

        // Load composite key unlock states
        _unlockedThemesByComposite.value = loadStringSet(KEY_UNLOCKED_THEMES_BY_COMPOSITE)
        _unlockedPacksByComposite.value = loadStringSet(KEY_UNLOCKED_PACKS_BY_COMPOSITE)
        _unlockedWallpapersByUrl.value = loadStringSet(KEY_UNLOCKED_WALLPAPERS_BY_URL)
    }

    /**
     * Helper function to load integer set from SharedPreferences
     */
    private fun loadIntSet(key: String): ImmutableSet<Int> {
        val savedString = preferenceProvider.get<String>(key) ?: ""
        return if (savedString.isNotEmpty()) {
            savedString.split(SEPARATOR)
                .mapNotNull { it.toIntOrNull() }
                .toImmutableSet()
        } else {
            persistentSetOf()
        }
    }

    /**
     * Helper function to load string set from SharedPreferences
     */
    private fun loadStringSet(key: String): ImmutableSet<String> {
        val savedString = preferenceProvider.get<String>(key) ?: ""
        return if (savedString.isNotEmpty()) {
            savedString.split(SEPARATOR).toImmutableSet()
        } else {
            persistentSetOf()
        }
    }

    /**
     * Helper function to save integer set to SharedPreferences
     */
    private fun saveIntSet(key: String, set: ImmutableSet<Int>) {
        val stringValue = set.joinToString(SEPARATOR)
        preferenceProvider.save<String>(key, stringValue)
    }

    /**
     * Helper function to save string set to SharedPreferences
     */
    private fun saveStringSet(key: String, set: ImmutableSet<String>) {
        val stringValue = set.joinToString(SEPARATOR)
        preferenceProvider.save<String>(key, stringValue)
    }

    // ============= THEME UNLOCK METHODS =============

    /**
     * Check if a theme is unlocked
     */
    fun isThemeUnlocked(themeId: Int): Boolean {
        return _unlockedThemes.value.contains(themeId)
    }

    /**
     * Unlock a theme
     */
    suspend fun unlockTheme(themeId: Int) {
        _unlockedThemes.update { currentSet ->
            val newSet = currentSet.add(themeId)
            saveIntSet(KEY_UNLOCKED_THEMES, newSet)
            newSet
        }
    }

    /**
     * Lock a theme (for testing purposes)
     */
    suspend fun lockTheme(themeId: Int) {
        _unlockedThemes.update { currentSet ->
            val newSet = currentSet.remove(themeId)
            saveIntSet(KEY_UNLOCKED_THEMES, newSet)
            newSet
        }
    }

    // ============= WALLPAPER PACK UNLOCK METHODS =============

    /**
     * Check if a wallpaper pack is unlocked
     */
    fun isWallpaperPackUnlocked(packId: Int): Boolean {
        return _unlockedWallpaperPacks.value.contains(packId)
    }

    /**
     * Unlock a wallpaper pack
     */
    suspend fun unlockWallpaperPack(packId: Int) {
        _unlockedWallpaperPacks.update { currentSet ->
            val newSet = currentSet.add(packId)
            saveIntSet(KEY_UNLOCKED_WALLPAPER_PACKS, newSet)
            newSet
        }
    }

    // ============= WIDGET PACK UNLOCK METHODS =============

    /**
     * Check if a widget pack is unlocked
     */
    fun isWidgetPackUnlocked(packId: Int): Boolean {
        return _unlockedWidgetPacks.value.contains(packId)
    }

    /**
     * Unlock a widget pack
     */
    suspend fun unlockWidgetPack(packId: Int) {
        _unlockedWidgetPacks.update { currentSet ->
            val newSet = currentSet.add(packId)
            saveIntSet(KEY_UNLOCKED_WIDGET_PACKS, newSet)
            newSet
        }
    }

    // ============= ICON PACK UNLOCK METHODS =============

    /**
     * Check if an icon pack is unlocked
     */
    fun isIconPackUnlocked(packId: Int): Boolean {
        return _unlockedIconPacks.value.contains(packId)
    }

    /**
     * Unlock an icon pack
     */
    suspend fun unlockIconPack(packId: Int) {
        _unlockedIconPacks.update { currentSet ->
            val newSet = currentSet.add(packId)
            saveIntSet(KEY_UNLOCKED_ICON_PACKS, newSet)
            newSet
        }
    }

    // ============= INDIVIDUAL WALLPAPER UNLOCK METHODS =============

    /**
     * Check if a specific wallpaper is unlocked
     */
    fun isWallpaperUnlocked(wallpaperUrl: String): Boolean {
        return _unlockedWallpapers.value.contains(wallpaperUrl)
    }

    /**
     * Unlock a specific wallpaper
     */
    suspend fun unlockWallpaper(wallpaperUrl: String) {
        _unlockedWallpapers.update { currentSet ->
            val newSet = currentSet.add(wallpaperUrl)
            saveStringSet(KEY_UNLOCKED_WALLPAPERS, newSet)
            newSet
        }
    }

    // ============= GENERIC UNLOCK METHODS =============

    /**
     * Unlock any pack by type and ID
     */
    suspend fun unlockPack(packType: PackType, packId: Int) {
        when (packType) {
            PackType.WALLPAPER -> unlockWallpaperPack(packId)
            PackType.WIDGET -> unlockWidgetPack(packId)
            PackType.ICON -> unlockIconPack(packId)
        }
    }

    /**
     * Check if any pack is unlocked by type and ID
     */
    fun isPackUnlocked(packType: PackType, packId: Int): Boolean {
        return when (packType) {
            PackType.WALLPAPER -> isWallpaperPackUnlocked(packId)
            PackType.WIDGET -> isWidgetPackUnlocked(packId)
            PackType.ICON -> isIconPackUnlocked(packId)
        }
    }

    // ============= BULK OPERATIONS =============

    /**
     * Get all unlocked items as a summary
     */
    fun getUnlockSummary(): Flow<UnlockSummary> = flow {
        emit(
            UnlockSummary(
                unlockedThemes = _unlockedThemes.value,
                unlockedWallpaperPacks = _unlockedWallpaperPacks.value,
                unlockedWidgetPacks = _unlockedWidgetPacks.value,
                unlockedIconPacks = _unlockedIconPacks.value,
                unlockedWallpapers = _unlockedWallpapers.value
            )
        )
    }

    /**
     * Clear all unlock states (for testing or reset purposes)
     */
    suspend fun clearAllUnlockStates() {
        // Clear ID-based states
        _unlockedThemes.value = persistentSetOf()
        _unlockedWallpaperPacks.value = persistentSetOf()
        _unlockedWidgetPacks.value = persistentSetOf()
        _unlockedIconPacks.value = persistentSetOf()
        _unlockedWallpapers.value = persistentSetOf()

        preferenceProvider.save<String>(KEY_UNLOCKED_THEMES, "")
        preferenceProvider.save<String>(KEY_UNLOCKED_WALLPAPER_PACKS, "")
        preferenceProvider.save<String>(KEY_UNLOCKED_WIDGET_PACKS, "")
        preferenceProvider.save<String>(KEY_UNLOCKED_ICON_PACKS, "")
        preferenceProvider.save<String>(KEY_UNLOCKED_WALLPAPERS, "")

        // Clear composite key states
        _unlockedThemesByComposite.value = persistentSetOf()
        _unlockedPacksByComposite.value = persistentSetOf()
        _unlockedWallpapersByUrl.value = persistentSetOf()

        preferenceProvider.save<String>(KEY_UNLOCKED_THEMES_BY_COMPOSITE, "")
        preferenceProvider.save<String>(KEY_UNLOCKED_PACKS_BY_COMPOSITE, "")
        preferenceProvider.save<String>(KEY_UNLOCKED_WALLPAPERS_BY_URL, "")
    }

    // ============= COMPOSITE KEY UNLOCK METHODS =============
    // These methods are for APIs that don't provide IDs

    /**
     * Unlock theme by composite key (categoryTitle + themeTitle)
     */
    suspend fun unlockThemeByComposite(categoryTitle: String, themeTitle: String) {
        val compositeKey = "${categoryTitle}${COMPOSITE_SEPARATOR}${themeTitle}"
        _unlockedThemesByComposite.update { currentSet ->
            val newSet = currentSet.add(compositeKey)
            saveStringSet(KEY_UNLOCKED_THEMES_BY_COMPOSITE, newSet)
            newSet
        }
    }

    /**
     * Check if theme is unlocked by composite key
     */
    fun isThemeUnlockedByComposite(categoryTitle: String, themeTitle: String): Boolean {
        val compositeKey = "${categoryTitle}${COMPOSITE_SEPARATOR}${themeTitle}"
        return _unlockedThemesByComposite.value.contains(compositeKey)
    }

    /**
     * Unlock pack by composite key (themeTitle + packTitle + packType)
     */
    suspend fun unlockPackByComposite(themeTitle: String, packTitle: String, packType: String) {
        val compositeKey = "${themeTitle}${COMPOSITE_SEPARATOR}${packTitle}${COMPOSITE_SEPARATOR}${packType}"
        _unlockedPacksByComposite.update { currentSet ->
            val newSet = currentSet.add(compositeKey)
            saveStringSet(KEY_UNLOCKED_PACKS_BY_COMPOSITE, newSet)
            newSet
        }
    }

    /**
     * Check if pack is unlocked by composite key
     */
    fun isPackUnlockedByComposite(themeTitle: String, packTitle: String, packType: String): Boolean {
        val compositeKey = "${themeTitle}${COMPOSITE_SEPARATOR}${packTitle}${COMPOSITE_SEPARATOR}${packType}"
        return _unlockedPacksByComposite.value.contains(compositeKey)
    }

    /**
     * Unlock wallpaper by URL (for individual wallpapers)
     */
    suspend fun unlockWallpaperByUrl(wallpaperUrl: String) {
        _unlockedWallpapersByUrl.update { currentSet ->
            val newSet = currentSet.add(wallpaperUrl)
            saveStringSet(KEY_UNLOCKED_WALLPAPERS_BY_URL, newSet)
            newSet
        }
    }

    /**
     * Check if wallpaper is unlocked by URL
     */
    fun isWallpaperUnlockedByUrl(wallpaperUrl: String): Boolean {
        return _unlockedWallpapersByUrl.value.contains(wallpaperUrl)
    }
}

/**
 * Data class representing the summary of all unlock states
 */
data class UnlockSummary(
    val unlockedThemes: ImmutableSet<Int>,
    val unlockedWallpaperPacks: ImmutableSet<Int>,
    val unlockedWidgetPacks: ImmutableSet<Int>,
    val unlockedIconPacks: ImmutableSet<Int>,
    val unlockedWallpapers: ImmutableSet<String>
)
