package com.amobear.themepack.presentation.wallpaper_install

import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.amobear.themepack.R
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.presentation.home.components.UnlockWallpaperBottomSheet
import com.amobear.themepack.presentation.wallpaper_install.component.SetWallpaperBottomSheet
import com.amobear.themepack.presentation.icon_install.IconInstallIntent
import com.amobear.themepack.presentation.main.navigation.AppLocalNavController
import com.amobear.themepack.presentation.wallpaper_generator.WallpaperGeneratorDestination
import com.amobear.themepack.presentation.wallpaper_install.component.PersonalizedWallpaperDialog
import com.amobear.themepack.presentation.wallpaper_install.viewmodel.ThemeInstallViewModelInterface
import com.amobear.themepack.presentation.wallpaper_install.viewmodel.WallpaperInstallViewModel
import com.amobear.themepack.presentation.wallpaper_install.viewmodel.WallpaperUIEffect
import com.amobear.themepack.presentation.wallpaper_install.viewmodel.WallpaperUiState
import com.amobear.themepack.ui.common.InstallTopAppBar
import com.amobear.themepack.ui.common.StepsIndicator
import timber.log.Timber

/**
 * Screen for displaying and setting themes/Themes
 */
@Composable
fun WallpaperInstallScreen(
    theme: Theme,
    onBack: () -> Unit,
    onNavigateToSuccess: (Theme) -> Unit = {},
    viewModel: ThemeInstallViewModelInterface = hiltViewModel<WallpaperInstallViewModel>()
) {
    val uiState by viewModel.uiState.collectAsState()
    val navController = AppLocalNavController.current
    LaunchedEffect(Unit) {
        viewModel.effects.collect {
            when (it) {
                WallpaperUIEffect.DismissPersonalizedWallpaperDialog -> {

                }

                WallpaperUIEffect.DismissUnlockWallpaperBottomSheet -> {

                }

                WallpaperUIEffect.NavigateToAIWallpaper -> {
                    navController?.navigate(WallpaperGeneratorDestination.route) {
                        launchSingleTop = true
                    }
                }

                is WallpaperUIEffect.NavigateToSuccess -> {
                    onNavigateToSuccess(it.theme)
                }

                WallpaperUIEffect.ShowPersonalizedWallpaperDialog -> {

                }

                WallpaperUIEffect.ShowUnlockWallpaperBottomSheet -> {

                }

                WallpaperUIEffect.GoPremium -> {}
                is WallpaperUIEffect.UnlockWallpaperWithCoins -> {}
                WallpaperUIEffect.ClearError -> {}
                is WallpaperUIEffect.LoadTheme -> {}
                WallpaperUIEffect.ShowSetWallpaperBottomSheet -> {}
                WallpaperUIEffect.DismissSetWallpaperBottomSheet -> {}
                is WallpaperUIEffect.SetWallpaper -> {}
            }
        }
    }

    // Load theme by ID when it changes
    LaunchedEffect(theme) {
        viewModel.handleEffect(WallpaperUIEffect.LoadTheme(theme))
    }

    // Handle errors
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            Timber.tag("WallpaperInstall").e("Error: $error")
            // Clear error after showing it
            viewModel.handleEffect(WallpaperUIEffect.ClearError)
        }
    }

    WallPaperInstallContent(
        onBack,
        uiState,
        handleIntent = viewModel::handleEffect
    )
}

@Composable
private fun WallPaperInstallContent(
    onBack: () -> Unit,
    uiState: WallpaperUiState,
    handleIntent: (WallpaperUIEffect) -> Unit = {}
) {
    val backgroundColor = Color(0xfff6f2f2)
    val context = LocalContext.current
    Scaffold(topBar = {
        HeaderContent(
            onBackClick = onBack, currentCoins = uiState.userCoins, processIntent = {
                Toast.makeText(context, "Not implemented yet", Toast.LENGTH_SHORT).show()
            }, backgroundColor = backgroundColor
        )
    }, bottomBar = {}) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(it)
                .background(backgroundColor),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            StepsIndicator(
                currentActiveStep = 1,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            WallpaperMainContent(uiState, handleIntent)
            if (uiState.isUnlock) {
                RegenerateWithAIButton(onClick = {
                    handleIntent(WallpaperUIEffect.NavigateToAIWallpaper)
                })
            } else {
                UnlockWallpaperButton(
                    showUnlockWallpaperBottomSheet = { handleIntent(WallpaperUIEffect.ShowUnlockWallpaperBottomSheet) },
                    uiState
                )
            }

            BannerAds()

            // Unlock Wallpaper Bottom Sheet
            if (uiState.isShowUnlockWallpaperBottomSheet) {
                UnlockWallpaperBottomSheet(
                    requiredCoins = uiState.wallpaperPrice,
                    currentCoins = uiState.userCoins,
                    onUseCoins = {
                        handleIntent(WallpaperUIEffect.UnlockWallpaperWithCoins(uiState.wallpaperPrice))
                    },
                    onGoPremium = {
                        handleIntent(WallpaperUIEffect.GoPremium)
                    },
                    onDismiss = {
                        handleIntent(WallpaperUIEffect.DismissUnlockWallpaperBottomSheet)
                    })
            }

            // Personalized Wallpaper Dialog
            if (uiState.isShowPersonalizedWallpaperDialog) {
                PersonalizedWallpaperDialog(
                    onTryNow = {
                        handleIntent(WallpaperUIEffect.NavigateToAIWallpaper)
                    },
                    onSkip = { handleIntent(WallpaperUIEffect.DismissPersonalizedWallpaperDialog) },
                    onDismiss = { handleIntent(WallpaperUIEffect.DismissPersonalizedWallpaperDialog) })
            }

            // Set Wallpaper Bottom Sheet
            if (uiState.isShowSetWallpaperBottomSheet) {
                SetWallpaperBottomSheet(
                    onDismiss = {
                        handleIntent(WallpaperUIEffect.DismissSetWallpaperBottomSheet)
                    },
                    onSetWallpaper = { wallpaperType ->
                        handleIntent(WallpaperUIEffect.SetWallpaper(wallpaperType))
                    },
                    isInstalling = uiState.isInstalling
                )
            }
        }
    }
}

@Composable
private fun BannerAds() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(50.dp)
            .background(Color(0xFFECECEC))
            .border(
                width = 1.dp, color = Color.Black, shape = RoundedCornerShape(8.dp)
            ), contentAlignment = Alignment.Center
    ) {
        Text(
            text = "Banner Ad Space", color = Color.Gray, fontSize = 14.sp
        )
    }
}

@Composable
private fun UnlockWallpaperButton(
    showUnlockWallpaperBottomSheet: () -> Unit, uiState: WallpaperUiState
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 16.dp)
            .height(82.dp)
            .background(Color.White)
            .padding(16.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(Color(0xFF015BFF))
            .clickable {
                // Show unlock wallpaper bottom sheet
                showUnlockWallpaperBottomSheet()
            }, contentAlignment = Alignment.Center
    ) {
        if (uiState.isUnlock) {
            CircularProgressIndicator(
                color = Color.White, modifier = Modifier.size(20.dp), strokeWidth = 2.dp
            )
        } else {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_unlock),
                    contentDescription = "Unlock",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = when {
                        uiState.isUnlock -> "Downloading..."
                        else -> "Unlock Wallpaper"
                    }, color = Color.White, fontSize = 16.sp, fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Composable
private fun GetFreeButton(
    modifier: Modifier = Modifier, onClick: () -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(50.dp)
            .padding(horizontal = 12.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(Color(0xCC015BFF))
            .clickable {
                onClick()
            }) {
        Icon(
            painter = painterResource(id = R.drawable.ic_coin),
            contentDescription = null,
            tint = Color.White,
            modifier = Modifier
                .align(Alignment.CenterStart)
                .padding(start = 16.dp)
                .size(24.dp)
        )

        Column(
            modifier = Modifier.align(Alignment.Center)
        ) {

            Text(
                text = "Get Free",
                color = Color.White,
                fontSize = 14.sp,
                fontFamily = FontFamily.Default, // Quicksand
                fontWeight = FontWeight.Bold,
                lineHeight = 14.sp, // height: 1
                textAlign = TextAlign.Center,
                modifier = Modifier
            )
            Text(
                text = "Watch an ads",
                color = Color.White,
                fontSize = 10.sp,
                fontFamily = FontFamily.Default, // Quicksand
                fontWeight = FontWeight.Medium,
                lineHeight = 10.sp, // height: 1
                textAlign = TextAlign.Center,
                modifier = Modifier
            )
        }
    }
}

@Composable
private fun ColumnScope.WallpaperMainContent(
    uiState: WallpaperUiState,
    handleIntent: (WallpaperUIEffect) -> Unit = {}
) {
    Box(
        modifier = Modifier
            .fillMaxWidth(0.8f)
            .weight(1f)
            .clip(RoundedCornerShape(16.dp))
            .background(Color.White), contentAlignment = Alignment.Center
    ) {
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = Color(0xFF015BFF)
                    )
                }
            }

            uiState.theme != null -> {
                val context = LocalContext.current
                Image(
                    painter = painterResource(id = R.drawable.theme_preview),
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .size(height = 33.dp, width = 38.dp)
                        .align(Alignment.Center)
                )

                AsyncImage(
                    model = ImageRequest.Builder(context).data(uiState.theme.wallpaperPacks.first().previewImage)
                        .crossfade(true).build(),
                    contentDescription = uiState.theme.title,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.fillMaxSize(),
                )
                if (uiState.isUnlock) {
                    Box(
                        modifier = Modifier
                            .padding(bottom = 16.dp)
                            .size(width = 80.dp, height = 32.dp)
                            .align(Alignment.BottomCenter)
                            .clip(RoundedCornerShape(20.dp))
                            .background(Color(0x99212121))
                            .clickable {
                                handleIntent(WallpaperUIEffect.ShowSetWallpaperBottomSheet)
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "Set",
                            textAlign = TextAlign.Center,
                            color = Color.White,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.W500
                        )
                    }
                } else {
                    GetFreeButton(
                        modifier = Modifier
                            .padding(bottom = 16.dp)
                            .align(Alignment.BottomCenter),
                        onClick = {
                            Toast.makeText(context, "No implementation yet", Toast.LENGTH_SHORT)
                                .show()
                        })
                }
            }

            else -> {
                Box(
                    modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Theme not found", color = Color.Gray, fontSize = 16.sp
                    )
                }
            }
        }
    }
}

@Composable
private fun HeaderContent(
    modifier: Modifier = Modifier,
    onBackClick: () -> Unit,
    currentCoins: Int,
    processIntent: (IconInstallIntent) -> Unit,
    backgroundColor: Color
) {
    InstallTopAppBar(
        "Install", currentCoins, {
            processIntent(IconInstallIntent.AddCoins)
        }, onBackClick, modifier, backgroundColor = backgroundColor
    )
}

/**
 * Re-Generate with AI button
 */
@Composable
fun RegenerateWithAIButton(
    onClick: () -> Unit, modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(100.dp))
            .background(
                brush = Brush.horizontalGradient(
                    colors = listOf(Color(0xFF8A3FFC), Color(0xFF33B1FF))
                )
            )
            .clickable(onClick = onClick)
            .padding(horizontal = 16.dp, vertical = 12.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_sparkle),
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(20.dp)
            )

            Text(
                text = "Re-Generate with AI",
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )

            Icon(
                painter = painterResource(id = R.drawable.ic_arrow_right),
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

@Composable
private fun SuccessDialog(
    isWallpaperSet: Boolean, onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        containerColor = Color.White,
        shape = RoundedCornerShape(16.dp),
        title = {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_check_circle),
                    contentDescription = "Success",
                    tint = Color(0xFF4CAF50),
                    modifier = Modifier.size(64.dp)
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = if (isWallpaperSet) "Wallpaper Set!" else "Download Complete!",
                    color = Color(0xFF212121),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
            }
        },
        text = {
            Text(
                text = if (isWallpaperSet) "Your wallpaper has been set successfully"
                else "Theme has been saved to your gallery",
                color = Color(0xFF757575),
                fontSize = 14.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        },
        confirmButton = {
            Button(
                onClick = onDismiss,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFFF76CE)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Text(
                    text = "OK", fontSize = 16.sp, fontWeight = FontWeight.Bold, color = Color.White
                )
            }
        })
}

@Preview(showBackground = true)
@Composable
private fun SuccessDialogPreview() {
    SuccessDialog(
        isWallpaperSet = true, onDismiss = {})
}

@Preview(showBackground = true)
@Composable
fun RegenerateWithAIButtonPreview() {
    RegenerateWithAIButton(
        onClick = {}, modifier = Modifier.width(280.dp)
    )
}

/**
 * Full preview showing all components and states of the WallpaperInstallScreen
 */
@Preview(showBackground = true, showSystemUi = true)
@Composable
fun WallpaperInstallScreenFullPreview() {
    // Mock UI state with all components visible
    val mockUiState = WallpaperUiState(
        theme = Theme(
            id = 1,
            title = "Beautiful Sunset",
            description = "A stunning sunset wallpaper",
            previewImage = "https://picsum.photos/400/600",
            categoryId = 1,
            wallpaperPacks = emptyList(),
            widgetPacks = emptyList(),
            iconPacks = emptyList(),
            downloadDate = null,
            isUnlocked = true,
            localPath = null,
            isFromCache = false,
            matchConfidence = 1.0f
        ),
        isLoading = false,
        isUnlock = true,
        isShowThemeOptions = false,
        isShowUnlockWallpaperBottomSheet = false,
        isShowPersonalizedWallpaperDialog = false,
        userCoins = 150,
        wallpaperPrice = 50,
        error = null
    )

    WallPaperInstallContent(
        onBack = {},
        uiState = mockUiState
    )
}

/**
 * Preview showing loading state
 */
@Preview(showBackground = true, showSystemUi = true)
@Composable
fun WallpaperInstallScreenLoadingPreview() {
    val mockUiState = WallpaperUiState(
        theme = null,
        isLoading = true,
        isUnlock = false,
        isShowThemeOptions = false,
        isShowUnlockWallpaperBottomSheet = false,
        isShowPersonalizedWallpaperDialog = false,
        userCoins = 75,
        wallpaperPrice = 50,
        error = null
    )

    WallPaperInstallContent(
        onBack = {},
        uiState = mockUiState,
    )
}

/**
 * Preview showing downloading state
 */
@Preview(showBackground = true, showSystemUi = true)
@Composable
fun WallpaperInstallScreenDownloadingPreview() {
    val mockUiState = WallpaperUiState(
        theme = Theme(
            id = 2,
            title = "Ocean Waves",
            description = "Calming ocean waves",
            previewImage = "https://picsum.photos/400/600?random=2",
            categoryId = 1,
            wallpaperPacks = emptyList(),
            widgetPacks = emptyList(),
            iconPacks = emptyList(),
            downloadDate = null,
            isUnlocked = true,
            localPath = null,
            isFromCache = false,
            matchConfidence = 1.0f
        ),
        isLoading = false,
        isUnlock = true,
        isShowThemeOptions = false,
        isShowUnlockWallpaperBottomSheet = false,
        isShowPersonalizedWallpaperDialog = false,
        userCoins = 200,
        wallpaperPrice = 0,
        error = null
    )

    WallPaperInstallContent(
        onBack = {},
        uiState = mockUiState,
    )
}

/**
 * Preview showing success message
 */
@Preview(showBackground = true, showSystemUi = true)
@Composable
fun WallpaperInstallScreenSuccessPreview() {
    val mockUiState = WallpaperUiState(
        theme = Theme(
            id = 3,
            title = "Mountain Peak",
            description = "Majestic mountain peak",
            previewImage = "https://picsum.photos/400/600?random=3",
            categoryId = 1,
            wallpaperPacks = emptyList(),
            widgetPacks = emptyList(),
            iconPacks = emptyList(),
            downloadDate = System.currentTimeMillis(),
            isUnlocked = true,
            localPath = "/storage/themes/mountain_peak",
            isFromCache = false,
            matchConfidence = 1.0f
        ),
        isLoading = false,
        isUnlock = false,
        isShowThemeOptions = false,
        isShowUnlockWallpaperBottomSheet = false,
        isShowPersonalizedWallpaperDialog = false,
        userCoins = 125,
        wallpaperPrice = 75,
        error = null
    )

    WallPaperInstallContent(
        onBack = {},
        uiState = mockUiState,
    )
}

/**
 * Preview showing unlock wallpaper bottom sheet
 */
@Preview(showBackground = true, showSystemUi = true)
@Composable
fun WallpaperInstallScreenUnlockBottomSheetPreview() {
    val mockUiState = WallpaperUiState(
        theme = Theme(
            id = 4,
            title = "City Lights",
            description = "Vibrant city lights at night",
            previewImage = "https://picsum.photos/400/600?random=4",
            categoryId = 2,
            wallpaperPacks = emptyList(),
            widgetPacks = emptyList(),
            iconPacks = emptyList(),
            downloadDate = null,
            isUnlocked = false,
            localPath = null,
            isFromCache = false,
            matchConfidence = 1.0f
        ),
        isLoading = false,
        isUnlock = false,
        isShowThemeOptions = false,
        isShowUnlockWallpaperBottomSheet = true,
        isShowPersonalizedWallpaperDialog = false,
        userCoins = 80,
        wallpaperPrice = 100,
        error = null
    )

    WallPaperInstallContent(
        onBack = {},
        uiState = mockUiState,
    )
}

/**
 * Preview showing error state (theme not found)
 */
@Preview(showBackground = true, showSystemUi = true)
@Composable
fun WallpaperInstallScreenErrorPreview() {
    val mockUiState = WallpaperUiState(
        theme = null,
        isLoading = false,
        isUnlock = false,
        isShowThemeOptions = false,
        isShowUnlockWallpaperBottomSheet = false,
        isShowPersonalizedWallpaperDialog = false,
        userCoins = 50,
        wallpaperPrice = 50,
        error = "Theme not found"
    )

    WallPaperInstallContent(
        onBack = {},
        uiState = mockUiState,
    )
}

/**
 * Preview showing set wallpaper bottom sheet
 */
@Preview(showBackground = true, showSystemUi = true)
@Composable
fun WallpaperInstallScreenSetWallpaperBottomSheetPreview() {
    val mockUiState = WallpaperUiState(
        theme = Theme(
            id = 5,
            title = "Forest Path",
            description = "Peaceful forest path",
            previewImage = "https://picsum.photos/400/600?random=5",
            categoryId = 1,
            wallpaperPacks = emptyList(),
            widgetPacks = emptyList(),
            iconPacks = emptyList(),
            downloadDate = null,
            isUnlocked = true,
            localPath = null,
            isFromCache = false,
            matchConfidence = 1.0f
        ),
        isLoading = false,
        isUnlock = true,
        isShowThemeOptions = false,
        isShowUnlockWallpaperBottomSheet = false,
        isShowSetWallpaperBottomSheet = true,
        isInstalling = false,
        isShowPersonalizedWallpaperDialog = false,
        userCoins = 150,
        wallpaperPrice = 50,
        error = null
    )

    WallPaperInstallContent(
        onBack = {},
        uiState = mockUiState,
    )
}