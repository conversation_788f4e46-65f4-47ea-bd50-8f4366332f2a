package com.amobear.themepack.presentation.profile

import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.WallpaperPack
import com.amobear.themepack.data.model.WidgetPack
import com.amobear.themepack.data.model.IconPack
import com.amobear.themepack.data.model.Wallpaper
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList

/**
 * UI state for Profile screen
 */
data class ProfileState(
    val isLoading: Boolean = false,
    val selectedTab: ProfileTab = ProfileTab.THEME,
    val error: String? = null,
    
    // Unlocked data (from database)
    val unlockedThemes: ImmutableList<Theme> = persistentListOf(),
    val unlockedWallpaperPacks: ImmutableList<WallpaperPack> = persistentListOf(),
    val unlockedWidgetPacks: ImmutableList<WidgetPack> = persistentListOf(),
    val unlockedIconPacks: ImmutableList<IconPack> = persistentListOf(),
    val unlockedWallpapers: ImmutableList<Wallpaper> = persistentListOf(),
    
    // Suggested data (stateless from API)
    val suggestedThemes: ImmutableList<Theme> = persistentListOf(),
    val suggestedWallpaperPacks: ImmutableList<WallpaperPack> = persistentListOf(),
    val suggestedWidgetPacks: ImmutableList<WidgetPack> = persistentListOf(),
    val suggestedIconPacks: ImmutableList<IconPack> = persistentListOf(),
    
    // UI state
    val isRefreshing: Boolean = false,
    val showEmptyState: Boolean = false
)

/**
 * Profile tabs
 */
enum class ProfileTab {
    THEME,
    WALLPAPER,
    WIDGET
}

/**
 * Helper extensions for ProfileState
 */
fun ProfileState.getCurrentUnlockedItems(): ImmutableList<Any> {
    return when (selectedTab) {
        ProfileTab.THEME -> unlockedThemes.map { it as Any }.toImmutableList()
        ProfileTab.WALLPAPER -> (unlockedWallpaperPacks + unlockedWallpapers).map { it as Any }.toImmutableList()
        ProfileTab.WIDGET -> unlockedWidgetPacks.map { it as Any }.toImmutableList()
    }
}

fun ProfileState.getCurrentSuggestedItems(): ImmutableList<Any> {
    return when (selectedTab) {
        ProfileTab.THEME -> suggestedThemes
        ProfileTab.WALLPAPER -> suggestedWallpaperPacks
        ProfileTab.WIDGET -> suggestedWidgetPacks
    }
}

fun ProfileState.hasUnlockedItems(): Boolean {
    return when (selectedTab) {
        ProfileTab.THEME -> unlockedThemes.isNotEmpty()
        ProfileTab.WALLPAPER -> unlockedWallpaperPacks.isNotEmpty() || unlockedWallpapers.isNotEmpty()
        ProfileTab.WIDGET -> unlockedWidgetPacks.isNotEmpty()
    }
}
