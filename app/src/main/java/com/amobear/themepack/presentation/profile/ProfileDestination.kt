package com.amobear.themepack.presentation.profile

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable

/**
 * Navigation destination for Profile screen
 */
data object ProfileDestination {
    const val route: String = "profile_route"
    const val destination: String = "profile_destination"
}

/**
 * Navigation graph extension for Profile screen
 */
fun NavGraphBuilder.profileGraph(
    onNavigateBack: () -> Unit,
) {
    composable(route = ProfileDestination.route) {
        ProfileRoute(onNavigateBack = onNavigateBack)
    }
}
