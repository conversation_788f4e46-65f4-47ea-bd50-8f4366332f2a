package com.amobear.themepack.presentation.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amobear.themepack.data.repository.StatelessThemeRepository
import com.amobear.themepack.data.repository.UnlockStateRepository
import com.amobear.themepack.data.repository.CompositeThemeRepository
import com.amobear.themepack.data.network.ApiResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import kotlinx.collections.immutable.toImmutableList
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val statelessThemeRepository: StatelessThemeRepository,
    private val unlockStateRepository: UnlockStateRepository,
    private val compositeThemeRepository: CompositeThemeRepository
) : ViewModel() {

    private val _state = MutableStateFlow(ProfileState())
    val state: StateFlow<ProfileState> = _state.asStateFlow()

    init {
        // Observe unlocked data changes
        observeUnlockedData()
    }

    fun processIntent(intent: ProfileIntent) {
        when (intent) {
            is ProfileIntent.Initialize -> initialize()
            is ProfileIntent.SelectTab -> selectTab(intent.tab)
            is ProfileIntent.Refresh -> refresh()
            is ProfileIntent.OnUnlockedItemClick -> handleUnlockedItemClick(intent.item)
            is ProfileIntent.OnSuggestedItemClick -> handleSuggestedItemClick(intent.item)
            is ProfileIntent.OnFindOutMoreClick -> handleFindOutMoreClick()
            is ProfileIntent.ClearError -> clearError()
        }
    }

    private fun initialize() {
        loadSuggestedData()
    }

    private fun selectTab(tab: ProfileTab) {
        _state.update { it.copy(selectedTab = tab) }
    }

    private fun refresh() {
        _state.update { it.copy(isRefreshing = true) }
        loadSuggestedData(forceRefresh = true)
    }

    private fun observeUnlockedData() {
        viewModelScope.launch {
            combine(
                compositeThemeRepository.themeCategoriesWithUnlockStatus,
                compositeThemeRepository.widgetCategoriesWithUnlockStatus,
                compositeThemeRepository.iconCategoriesWithUnlockStatus
            ) { themeCategories, widgetCategories, iconCategories ->
                
                // Extract unlocked items
                val unlockedThemes = themeCategories
                    .flatMap { it.themes }
                    .filter { it.isUnlocked }
                    .toImmutableList()

                val unlockedWallpaperPacks = themeCategories
                    .flatMap { it.themes }
                    .flatMap { it.wallpaperPacks }
                    .filter { it.isPurchased }
                    .toImmutableList()

                val unlockedWallpapers = themeCategories
                    .flatMap { it.themes }
                    .flatMap { it.wallpaperPacks }
                    .flatMap { it.wallpapers }
                    .filter { it.isUnlocked }
                    .toImmutableList()

                val unlockedWidgetPacks = widgetCategories
                    .flatMap { it.widgetPacks }
                    .filter { it.isPurchased }
                    .toImmutableList()

                val unlockedIconPacks = iconCategories
                    .flatMap { it.iconPacks }
                    .filter { it.isPurchased }
                    .toImmutableList()

                _state.update { currentState ->
                    currentState.copy(
                        unlockedThemes = unlockedThemes,
                        unlockedWallpaperPacks = unlockedWallpaperPacks,
                        unlockedWallpapers = unlockedWallpapers,
                        unlockedWidgetPacks = unlockedWidgetPacks,
                        unlockedIconPacks = unlockedIconPacks,
                        showEmptyState = unlockedThemes.isEmpty() && 
                                       unlockedWallpaperPacks.isEmpty() && 
                                       unlockedWallpapers.isEmpty() &&
                                       unlockedWidgetPacks.isEmpty() &&
                                       unlockedIconPacks.isEmpty()
                    )
                }
            }.collect { }
        }
    }

    private fun loadSuggestedData(forceRefresh: Boolean = false) {
        viewModelScope.launch {
            _state.update { it.copy(isLoading = !forceRefresh, error = null) }

            try {
                // Load suggested themes
                val themesResult = statelessThemeRepository.fetchThemeCategories(forceRefresh)
                when (themesResult) {
                    is ApiResult.Success -> {
                        val suggestedThemes = themesResult.data
                            .flatMap { it.themes }
                            .take(10) // Limit suggestions
                            .toImmutableList()

                        val suggestedWallpaperPacks = themesResult.data
                            .flatMap { it.themes }
                            .flatMap { it.wallpaperPacks }
                            .take(10)
                            .toImmutableList()

                        _state.update { currentState ->
                            currentState.copy(
                                suggestedThemes = suggestedThemes,
                                suggestedWallpaperPacks = suggestedWallpaperPacks,
                                isLoading = false,
                                isRefreshing = false
                            )
                        }
                    }
                    is ApiResult.Error -> {
                        Timber.e(themesResult.exception, "Error loading suggested themes")
                        _state.update { 
                            it.copy(
                                isLoading = false, 
                                isRefreshing = false,
                                error = themesResult.exception.message
                            ) 
                        }
                    }
                    is ApiResult.Loading -> {
                        // Already handled above
                    }
                }

                // Load suggested widgets
                val widgetsResult = statelessThemeRepository.fetchWidgetCategories(forceRefresh)
                when (widgetsResult) {
                    is ApiResult.Success -> {
                        val suggestedWidgetPacks = widgetsResult.data
                            .flatMap { it.widgetPacks }
                            .take(10)
                            .toImmutableList()

                        _state.update { currentState ->
                            currentState.copy(suggestedWidgetPacks = suggestedWidgetPacks)
                        }
                    }
                    is ApiResult.Error -> {
                        Timber.e(widgetsResult.exception, "Error loading suggested widgets")
                    }
                    is ApiResult.Loading -> {
                        // Handled above
                    }
                }

            } catch (e: Exception) {
                Timber.e(e, "Error loading suggested data")
                _state.update { 
                    it.copy(
                        isLoading = false, 
                        isRefreshing = false,
                        error = e.message
                    ) 
                }
            }
        }
    }

    private fun handleUnlockedItemClick(item: Any) {
        // TODO: Navigate to item detail or apply item
        Timber.d("Unlocked item clicked: $item")
    }

    private fun handleSuggestedItemClick(item: Any) {
        // TODO: Navigate to item detail or purchase flow
        Timber.d("Suggested item clicked: $item")
    }

    private fun handleFindOutMoreClick() {
        // TODO: Navigate to store or discovery screen
        Timber.d("Find out more clicked")
    }

    private fun clearError() {
        _state.update { it.copy(error = null) }
    }
}
