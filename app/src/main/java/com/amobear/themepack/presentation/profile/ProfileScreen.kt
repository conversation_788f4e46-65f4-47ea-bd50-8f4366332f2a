package com.amobear.themepack.presentation.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.amobear.themepack.data.model.Theme
import com.amobear.themepack.data.model.WallpaperPack
import com.amobear.themepack.data.model.WidgetPack
import kotlinx.collections.immutable.persistentListOf

// 1. Route (Stateful)
@Composable
fun ProfileRoute(
    onNavigateBack: () -> Unit,
    viewModel: ProfileViewModel = hiltViewModel()
) {
    val state by viewModel.state.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        viewModel.processIntent(ProfileIntent.Initialize)
    }

    ProfileScreen(
        state = state,
        onIntent = viewModel::processIntent,
        onNavigateBack = onNavigateBack
    )
}

// 2. Screen (Stateless)
@Composable
fun ProfileScreen(
    state: ProfileState,
    onIntent: (ProfileIntent) -> Unit,
    onNavigateBack: () -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF6F2F2))
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // Scrollable content
            Column(
                modifier = Modifier
                    .weight(1f)
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp)
            ) {
                Spacer(modifier = Modifier.height(12.dp))
                
                // Tab selector
                ProfileTabSelector(
                    selectedTab = state.selectedTab,
                    onTabSelected = { onIntent(ProfileIntent.SelectTab(it)) }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // White divider
                HorizontalDivider(
                    color = Color.White,
                    thickness = 2.dp
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // My Library Section
                MyLibrarySection(
                    state = state,
                    onIntent = onIntent
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Suggest Section
                SuggestSection(
                    state = state,
                    onIntent = onIntent
                )
                
                Spacer(modifier = Modifier.height(32.dp))
            }
        }
        
        // Bottom section
        Box(
            modifier = Modifier.align(Alignment.BottomCenter)
        ) {
            ProfileBottomSection()
        }
    }
}

@Composable
private fun ProfileTabSelector(
    selectedTab: ProfileTab,
    onTabSelected: (ProfileTab) -> Unit
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(6.dp)
    ) {
        ProfileTab.entries.forEach { tab ->
            ProfileTabItem(
                tab = tab,
                isSelected = selectedTab == tab,
                onClick = { onTabSelected(tab) }
            )
        }
    }
}

@Composable
private fun ProfileTabItem(
    tab: ProfileTab,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor = if (isSelected) Color(0xFFFF76CE) else Color.White
    val textColor = if (isSelected) Color.White else Color.Black
    
    Box(
        modifier = Modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(16.dp)
            )
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Text(
            text = when (tab) {
                ProfileTab.THEME -> "Theme"
                ProfileTab.WALLPAPER -> "Wallpaper"
                ProfileTab.WIDGET -> "Widget"
            },
            color = textColor,
            fontSize = 14.sp,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
private fun MyLibrarySection(
    state: ProfileState,
    onIntent: (ProfileIntent) -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "My Library",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )
            
            Button(
                onClick = { onIntent(ProfileIntent.OnFindOutMoreClick) },
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFFF76CE)
                ),
                shape = RoundedCornerShape(20.dp)
            ) {
                Text(
                    text = "Find out more",
                    color = Color.White,
                    fontSize = 12.sp
                )
            }
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        if (state.hasUnlockedItems()) {
            // Show unlocked items in multiple rows
            val unlockedItems = state.getCurrentUnlockedItems()
            val chunkedItems = unlockedItems.chunked(2) // 2 items per row
            
            chunkedItems.forEach { rowItems ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    rowItems.forEach { item ->
                        Box(
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(0.7f)
                                .background(
                                    color = Color.White,
                                    shape = RoundedCornerShape(12.dp)
                                )
                                .padding(8.dp)
                        ) {
                            // TODO: Display item content based on type
                            Text(
                                text = when (item) {
                                    is Theme -> item.title
                                    is WallpaperPack -> item.title
                                    is WidgetPack -> item.title
                                    else -> "Unknown"
                                },
                                fontSize = 12.sp
                            )
                        }
                    }
                    
                    // Fill remaining space if odd number of items
                    if (rowItems.size == 1) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
            }
        } else {
            // Empty state
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(120.dp)
                    .background(
                        color = Color.White.copy(alpha = 0.5f),
                        shape = RoundedCornerShape(12.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "No unlocked items yet",
                    color = Color.Gray,
                    fontSize = 14.sp
                )
            }
        }
    }
}

@Composable
private fun SuggestSection(
    state: ProfileState,
    onIntent: (ProfileIntent) -> Unit
) {
    Column {
        Text(
            text = "Suggest",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        val suggestedItems = state.getCurrentSuggestedItems()
        val chunkedItems = suggestedItems.take(6).chunked(3) // 3 items per row, max 6 items
        
        chunkedItems.forEach { rowItems ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                rowItems.forEach { item ->
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f)
                            .background(
                                color = Color.White,
                                shape = RoundedCornerShape(8.dp)
                            )
                            .padding(4.dp)
                    ) {
                        // TODO: Display suggested item content
                        Text(
                            text = when (item) {
                                is Theme -> item.title
                                is WallpaperPack -> item.title
                                is WidgetPack -> item.title
                                else -> "Item"
                            },
                            fontSize = 10.sp
                        )
                    }
                }
                
                // Fill remaining space if needed
                repeat(3 - rowItems.size) {
                    Spacer(modifier = Modifier.weight(1f))
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
        }
    }
}

@Composable
private fun ProfileBottomSection() {
    // TODO: Implement bottom section (maybe navigation or actions)
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(60.dp)
            .background(Color.White.copy(alpha = 0.9f))
    ) {
        // Placeholder for bottom content
    }
}

@Preview(showBackground = true, showSystemUi = true)
@Composable
private fun ProfileScreenPreview() {
    ProfileScreen(
        state = ProfileState(
            selectedTab = ProfileTab.WALLPAPER,
            unlockedThemes = persistentListOf(),
            suggestedThemes = persistentListOf()
        ),
        onIntent = {},
        onNavigateBack = {}
    )
}
