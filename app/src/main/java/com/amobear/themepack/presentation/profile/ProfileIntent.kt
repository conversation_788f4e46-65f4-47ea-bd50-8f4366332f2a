package com.amobear.themepack.presentation.profile

/**
 * User actions/intents for Profile screen
 */
sealed class ProfileIntent {
    
    /**
     * Initialize the screen - load unlocked and suggested data
     */
    data object Initialize : ProfileIntent()
    
    /**
     * Switch between tabs (Theme, Wallpaper, Widget)
     */
    data class SelectTab(val tab: ProfileTab) : ProfileIntent()
    
    /**
     * Refresh both unlocked and suggested data
     */
    data object Refresh : ProfileIntent()
    
    /**
     * User clicks on an unlocked item
     */
    data class OnUnlockedItemClick(val item: Any) : ProfileIntent()
    
    /**
     * User clicks on a suggested item
     */
    data class OnSuggestedItemClick(val item: Any) : ProfileIntent()
    
    /**
     * User clicks "Find out more" button
     */
    data object OnFindOutMoreClick : ProfileIntent()
    
    /**
     * Clear error state
     */
    data object ClearError : ProfileIntent()
}
